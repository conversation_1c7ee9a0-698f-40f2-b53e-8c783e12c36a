#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive PDF watermark remover for FluentPython PDF
Handles different watermark patterns found in different pages
"""
import sys
import pikepdf

def analyze_page_watermarks(pdf):
    """
    Analyze all pages to identify watermark patterns
    """
    watermark_patterns = {}
    
    print("Analyzing watermark patterns...")
    
    for page_num, page in enumerate(pdf.pages):
        try:
            resources = page.get("/Resources", {})
            if "/XObject" in resources:
                xobjects = resources["/XObject"]
                
                # Check each XObject to see if it might be a watermark
                for name, xobj in xobjects.items():
                    if hasattr(xobj, 'get'):
                        subtype = xobj.get("/Subtype", "Unknown")
                        
                        # Form XObjects are likely watermarks
                        if subtype == pikepdf.Name("/Form"):
                            if hasattr(xobj, 'read_bytes'):
                                try:
                                    content = xobj.read_bytes().decode('utf-8', errors='ignore')
                                    # Check if it references an image (likely watermark)
                                    if "Do" in content and any(img in content for img in ["/X4", "/Image1", "/Image"]):
                                        if name not in watermark_patterns:
                                            watermark_patterns[name] = []
                                        watermark_patterns[name].append(page_num)
                                except:
                                    pass
                        
                        # Large images might also be watermarks (check size)
                        elif subtype == pikepdf.Name("/Image"):
                            if hasattr(xobj, 'read_bytes'):
                                try:
                                    # If it's a very large image, it might be a watermark overlay
                                    size = len(xobj.read_bytes())
                                    if size > 4000000:  # > 4MB, likely a full-page watermark
                                        if name not in watermark_patterns:
                                            watermark_patterns[name] = []
                                        watermark_patterns[name].append(page_num)
                                except:
                                    pass
                                    
        except Exception as e:
            print(f"Error analyzing page {page_num + 1}: {e}")
            continue
    
    return watermark_patterns

def remove_watermarks_comprehensive(input_pdf, output_pdf):
    """
    Remove all identified watermarks from the PDF
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # First, analyze watermark patterns
            watermark_patterns = analyze_page_watermarks(pdf)
            print(f"Identified watermark patterns: {list(watermark_patterns.keys())}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Parse the content stream
                    operations = pikepdf.parse_content_stream(page, "")
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        # Skip Do operations that call watermark XObjects
                        if (str(operator) == "Do" and operands):
                            xobj_name = str(operands[0])
                            
                            # Check if this is a known watermark pattern
                            if xobj_name in watermark_patterns:
                                operations_removed += 1
                                page_modified = True
                                print(f"  Page {page_num + 1}: Removed Do {xobj_name} operation")
                                continue
                            
                            # Also check for common watermark names
                            if any(pattern in xobj_name for pattern in ["/Fm", "/X4", "/Image1"]):
                                # Additional check: see if this XObject exists in resources
                                try:
                                    resources = page.get("/Resources", {})
                                    if "/XObject" in resources and xobj_name in resources["/XObject"]:
                                        xobj = resources["/XObject"][xobj_name]
                                        subtype = xobj.get("/Subtype", "Unknown")
                                        
                                        # If it's a Form that might contain watermark
                                        if subtype == pikepdf.Name("/Form"):
                                            operations_removed += 1
                                            page_modified = True
                                            print(f"  Page {page_num + 1}: Removed Do {xobj_name} operation (Form)")
                                            continue
                                except:
                                    pass
                        
                        # Keep all other operations
                        new_operations.append((operands, operator))
                    
                    # Update the page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the modified PDF
            pdf.save(output_pdf)
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_all_watermarks.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_watermarks_comprehensive(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\nWatermark removal completed successfully!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
