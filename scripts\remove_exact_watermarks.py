#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Remove exact watermark patterns found in the PDF
"""
import sys
import pikepdf

def remove_exact_watermarks(input_pdf, output_pdf):
    """
    Remove the exact watermark patterns we identified
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    # The exact watermark pattern we found
    watermark_pattern = 'KWWSV˛JLWKXEFRPFKHQ\\]˝˙)OXHQW3\\WKRQQG&1'
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        should_remove = False
                        
                        # Check TJ operations specifically
                        if str(operator) == "TJ":
                            try:
                                # Convert to string and check for the exact pattern
                                operands_str = str(operands)
                                
                                # Check for the exact watermark pattern
                                if watermark_pattern in operands_str:
                                    should_remove = True
                                    operations_removed += 1
                                    page_modified = True
                                    
                                    if page_num < 10:  # Print for first 10 pages
                                        print(f"  Page {page_num + 1}: Removing exact watermark pattern")
                                
                                # Also check for partial patterns (in case of variations)
                                elif any(pattern in operands_str for pattern in [
                                    "KWWSV", "JLWKX", "FKHQ", "OXHQW", "WKRQ"
                                ]):
                                    should_remove = True
                                    operations_removed += 1
                                    page_modified = True
                                    
                                    if page_num < 10:
                                        print(f"  Page {page_num + 1}: Removing partial watermark pattern")
                                        
                            except Exception as e:
                                pass
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Text operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_exact_watermarks.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_exact_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Exact watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
