#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Remove all Do operations that call watermark XObjects
"""
import sys
import pikepdf

def remove_all_do_operations(input_pdf, output_pdf):
    """
    Remove all Do operations that call known watermark XObjects
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    # Known watermark XObject patterns
    watermark_xobjects = [
        "/Fm0", "/Fm1",  # Form XObjects
        "/X4", "/Image1",  # Image XObjects
    ]
    
    # Add all /Im patterns we found
    for i in range(2, 353):  # /Im2 to /Im352
        watermark_xobjects.append(f"/Im{i}")
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        should_remove = False
                        
                        # Check Do operations
                        if str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            
                            # Remove if it's a known watermark XObject
                            if xobj_name in watermark_xobjects:
                                should_remove = True
                                operations_removed += 1
                                page_modified = True
                                
                                if page_num < 5:  # Print for first 5 pages
                                    print(f"  Page {page_num + 1}: Removing Do {xobj_name}")
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Do operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_all_do_operations.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_all_do_operations(input_pdf, output_pdf):
        print("Failed to remove Do operations")
        sys.exit(1)
    
    print(f"\n🎉 All Do operations removed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
