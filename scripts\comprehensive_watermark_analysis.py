#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive watermark analysis - find where watermarks are really stored
"""
import sys
import pikepdf

def comprehensive_analysis(pdf_path):
    """
    Comprehensive analysis of watermark locations
    """
    print(f"Comprehensive analysis of: {pdf_path}")
    print("=" * 80)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Analyze multiple pages to find patterns
            pages_to_check = [0, 1, 2, 10, 50, 100]  # Check various pages
            
            for page_idx in pages_to_check:
                if page_idx >= len(pdf.pages):
                    continue
                    
                page = pdf.pages[page_idx]
                print(f"\n{'='*20} PAGE {page_idx + 1} {'='*20}")
                
                # 1. Check all page keys
                print(f"Page keys: {list(page.keys())}")
                
                # 2. Analyze content streams in detail
                if "/Contents" in page:
                    contents = page["/Contents"]
                    print(f"Contents type: {type(contents)}")
                    
                    if isinstance(contents, pikepdf.Array):
                        print(f"Multiple content streams: {len(contents)}")
                        
                        for i, stream in enumerate(contents):
                            try:
                                if hasattr(stream, 'read_bytes'):
                                    raw_content = stream.read_bytes()
                                    content_text = raw_content.decode('utf-8', errors='ignore')
                                    
                                    print(f"  Stream {i}: {len(content_text)} chars")
                                    
                                    # Look for watermark patterns
                                    patterns = [
                                        "KWWSV", "JLWKX", "FKHQ", "github", "chenyz1984", 
                                        "FluentPython", "https://", "/Fm", "Do"
                                    ]
                                    
                                    found_patterns = []
                                    for pattern in patterns:
                                        if pattern in content_text:
                                            found_patterns.append(pattern)
                                    
                                    if found_patterns:
                                        print(f"    *** PATTERNS FOUND: {found_patterns} ***")
                                        
                                        # Show context around patterns
                                        for pattern in found_patterns[:3]:  # Show first 3
                                            idx = content_text.find(pattern)
                                            if idx != -1:
                                                start = max(0, idx - 50)
                                                end = min(len(content_text), idx + 100)
                                                context = content_text[start:end]
                                                print(f"      {pattern} context: ...{context}...")
                                    else:
                                        print(f"    No watermark patterns found")
                                        
                            except Exception as e:
                                print(f"  Stream {i}: Error reading - {e}")
                    else:
                        # Single content stream
                        try:
                            if hasattr(contents, 'read_bytes'):
                                raw_content = contents.read_bytes()
                                content_text = raw_content.decode('utf-8', errors='ignore')
                                print(f"Single content stream: {len(content_text)} chars")
                                
                                # Check for patterns
                                patterns = [
                                    "KWWSV", "JLWKX", "FKHQ", "github", "chenyz1984", 
                                    "FluentPython", "https://", "/Fm", "Do"
                                ]
                                
                                found_patterns = []
                                for pattern in patterns:
                                    if pattern in content_text:
                                        found_patterns.append(pattern)
                                
                                if found_patterns:
                                    print(f"  *** PATTERNS FOUND: {found_patterns} ***")
                                else:
                                    print(f"  No watermark patterns found")
                                    
                        except Exception as e:
                            print(f"Single stream: Error reading - {e}")
                
                # 3. Check annotations
                if "/Annots" in page:
                    annots = page["/Annots"]
                    print(f"Annotations: {len(annots)} found")
                    
                    for i, annot in enumerate(annots):
                        try:
                            subtype = annot.get("/Subtype", "Unknown")
                            contents = annot.get("/Contents", "")
                            print(f"  Annotation {i}: {subtype} - {contents}")
                            
                            # Check all annotation properties
                            for key in annot.keys():
                                value = str(annot[key])
                                if any(pattern in value.lower() for pattern in ["github", "chenyz", "fluent"]):
                                    print(f"    *** WATERMARK in {key}: {value[:100]}... ***")
                                    
                        except Exception as e:
                            print(f"  Annotation {i}: Error - {e}")
                else:
                    print("No annotations")
                
                # 4. Check resources in detail
                if "/Resources" in page:
                    resources = page["/Resources"]
                    print(f"Resources: {list(resources.keys())}")
                    
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        print(f"XObjects: {list(xobjects.keys())}")
                        
                        for name, xobj in xobjects.items():
                            try:
                                subtype = xobj.get("/Subtype", "Unknown")
                                print(f"  {name}: {subtype}")
                                
                                if hasattr(xobj, 'read_bytes'):
                                    content = xobj.read_bytes()
                                    content_str = content.decode('utf-8', errors='ignore')
                                    
                                    if any(pattern in content_str for pattern in ["github", "chenyz", "KWWSV"]):
                                        print(f"    *** WATERMARK CONTENT in {name} ***")
                                        print(f"    Content: {content_str[:200]}...")
                                        
                            except Exception as e:
                                print(f"  {name}: Error - {e}")
                
                # 5. Parse content stream operations
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    print(f"Total operations: {len(operations)}")
                    
                    # Count different operation types
                    op_counts = {}
                    watermark_ops = []
                    
                    for i, (operands, operator) in enumerate(operations):
                        op_str = str(operator)
                        op_counts[op_str] = op_counts.get(op_str, 0) + 1
                        
                        # Check for watermark content
                        if op_str in ["TJ", "Tj"]:
                            operands_str = str(operands)
                            if any(pattern in operands_str for pattern in ["KWWSV", "JLWKX", "github"]):
                                watermark_ops.append((i, op_str, operands_str[:100]))
                        elif op_str == "Do":
                            xobj_name = str(operands[0]) if operands else ""
                            if any(pattern in xobj_name for pattern in ["/Fm", "/X", "/Image"]):
                                watermark_ops.append((i, op_str, xobj_name))
                    
                    print(f"Operation counts: {dict(list(op_counts.items())[:10])}")  # Show first 10
                    
                    if watermark_ops:
                        print(f"*** WATERMARK OPERATIONS FOUND: {len(watermark_ops)} ***")
                        for i, op_type, content in watermark_ops:
                            print(f"  Op {i}: {op_type} -> {content}")
                    else:
                        print("No watermark operations found")
                        
                except Exception as e:
                    print(f"Error parsing operations: {e}")
                
                print(f"{'='*60}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python comprehensive_watermark_analysis.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    comprehensive_analysis(pdf_file)
