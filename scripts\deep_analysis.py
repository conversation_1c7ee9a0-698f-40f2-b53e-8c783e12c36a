#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deep analysis of PDF watermarks - looking for text-based watermarks
"""
import sys
import pikepdf
import re

def deep_analyze_watermarks(pdf_path, pattern="https://github.com/chenyz1984/FluentPython2ndCN"):
    """
    Deep analysis to find all forms of watermarks
    """
    print(f"Deep analyzing: {pdf_path}")
    print(f"Looking for pattern: {pattern}")
    print("=" * 80)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Analyze first few pages in detail
            for page_num in range(min(3, len(pdf.pages))):
                page = pdf.pages[page_num]
                print(f"\n{'='*20} PAGE {page_num + 1} {'='*20}")
                
                # 1. Check all text operations in detail
                print("\n1. ANALYZING TEXT OPERATIONS:")
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    text_operations = []
                    
                    for i, (operands, operator) in enumerate(operations):
                        op_str = str(operator)
                        if op_str in ['Tj', 'TJ', "'", '"']:
                            text_operations.append((i, operands, operator))
                    
                    print(f"Found {len(text_operations)} text operations")
                    
                    for i, operands, operator in text_operations:
                        try:
                            if operands:
                                text_content = str(operands[0])
                                if pattern.lower() in text_content.lower() or "github" in text_content.lower():
                                    print(f"  *** FOUND PATTERN in operation {i}: {operator} -> {text_content}")
                                elif len(text_content) > 10:  # Show longer text for context
                                    print(f"  Operation {i}: {operator} -> {text_content[:50]}...")
                        except:
                            pass
                
                except Exception as e:
                    print(f"Error analyzing text operations: {e}")
                
                # 2. Check annotations in detail
                print("\n2. ANALYZING ANNOTATIONS:")
                try:
                    if "/Annots" in page:
                        annots = page["/Annots"]
                        print(f"Found {len(annots)} annotations")
                        
                        for i, annot in enumerate(annots):
                            try:
                                subtype = annot.get("/Subtype", "Unknown")
                                contents = annot.get("/Contents", "")
                                ap = annot.get("/AP", None)  # Appearance stream
                                
                                print(f"  Annotation {i}: Subtype={subtype}")
                                if contents:
                                    print(f"    Contents: {contents}")
                                    if pattern.lower() in str(contents).lower():
                                        print(f"    *** PATTERN FOUND IN ANNOTATION ***")
                                
                                if ap:
                                    print(f"    Has appearance stream: {ap}")
                                    # Try to analyze appearance stream
                                    if hasattr(ap, 'get') and "/N" in ap:
                                        normal_ap = ap["/N"]
                                        if hasattr(normal_ap, 'read_bytes'):
                                            try:
                                                ap_content = normal_ap.read_bytes().decode('utf-8', errors='ignore')
                                                if pattern in ap_content:
                                                    print(f"    *** PATTERN FOUND IN APPEARANCE STREAM ***")
                                                    print(f"    AP Content snippet: {ap_content[:200]}")
                                            except:
                                                pass
                                
                                # Check other annotation properties
                                for key in annot.keys():
                                    if key not in ["/Subtype", "/Contents", "/AP"]:
                                        try:
                                            value = str(annot[key])
                                            if pattern.lower() in value.lower():
                                                print(f"    *** PATTERN FOUND in {key}: {value}")
                                        except:
                                            pass
                                            
                            except Exception as e:
                                print(f"    Error analyzing annotation {i}: {e}")
                    else:
                        print("No annotations found")
                
                except Exception as e:
                    print(f"Error analyzing annotations: {e}")
                
                # 3. Check page-level objects
                print("\n3. ANALYZING PAGE-LEVEL OBJECTS:")
                try:
                    for key in page.keys():
                        try:
                            value = str(page[key])
                            if pattern.lower() in value.lower():
                                print(f"  *** PATTERN FOUND in page.{key}: {value[:100]}...")
                        except:
                            pass
                except Exception as e:
                    print(f"Error analyzing page objects: {e}")
                
                # 4. Raw content stream analysis
                print("\n4. RAW CONTENT STREAM ANALYSIS:")
                try:
                    # Get raw content stream
                    if hasattr(page, 'contents'):
                        if hasattr(page.contents, 'read_bytes'):
                            raw_content = page.contents.read_bytes()
                        else:
                            # Multiple content streams
                            raw_content = b""
                            if isinstance(page.contents, list):
                                for content in page.contents:
                                    if hasattr(content, 'read_bytes'):
                                        raw_content += content.read_bytes()
                        
                        raw_text = raw_content.decode('utf-8', errors='ignore')
                        
                        # Search for pattern in raw content
                        if pattern in raw_text:
                            print(f"  *** PATTERN FOUND IN RAW CONTENT STREAM ***")
                            # Find all occurrences
                            for match in re.finditer(re.escape(pattern), raw_text, re.IGNORECASE):
                                start = max(0, match.start() - 50)
                                end = min(len(raw_text), match.end() + 50)
                                context = raw_text[start:end]
                                print(f"    Context: ...{context}...")
                        
                        # Also search for parts of the pattern
                        if "github" in raw_text.lower():
                            print(f"  Found 'github' in raw content")
                        if "chenyz1984" in raw_text.lower():
                            print(f"  Found 'chenyz1984' in raw content")
                        if "fluentpython" in raw_text.lower():
                            print(f"  Found 'fluentpython' in raw content")
                
                except Exception as e:
                    print(f"Error analyzing raw content: {e}")
                
                # 5. Check for invisible/transparent text
                print("\n5. CHECKING FOR INVISIBLE TEXT:")
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    current_color = None
                    
                    for i, (operands, operator) in enumerate(operations):
                        op_str = str(operator)
                        
                        # Track color changes
                        if op_str in ['g', 'G', 'rg', 'RG']:  # Gray or RGB color
                            current_color = operands
                        
                        # Check text operations with current color
                        if op_str in ['Tj', 'TJ', "'", '"']:
                            try:
                                if operands:
                                    text_content = str(operands[0])
                                    if pattern.lower() in text_content.lower():
                                        print(f"  *** FOUND PATTERN with color {current_color}: {text_content}")
                            except:
                                pass
                
                except Exception as e:
                    print(f"Error checking invisible text: {e}")
                
                print(f"\n{'='*50}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python deep_analysis.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    deep_analyze_watermarks(pdf_file)
