#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final reality check - what exactly can we find in this PDF?
"""
import sys
import pikepdf

def final_reality_check(pdf_path):
    """
    Final comprehensive check of what's actually in the PDF
    """
    print(f"Final reality check: {pdf_path}")
    print("=" * 60)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check page 10 specifically (you can tell me if this page has watermarks)
            page_idx = 9  # Page 10 (0-based)
            if page_idx < len(pdf.pages):
                page = pdf.pages[page_idx]
                print(f"\n=== DETAILED ANALYSIS OF PAGE {page_idx + 1} ===")
                
                # 1. Check page structure
                print(f"Page keys: {list(page.keys())}")
                
                # 2. Check resources
                if "/Resources" in page:
                    resources = page["/Resources"]
                    print(f"Resources: {list(resources.keys())}")
                    
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        print(f"XObjects: {list(xobjects.keys())}")
                        
                        for name, xobj in xobjects.items():
                            subtype = xobj.get("/Subtype", "Unknown")
                            if hasattr(xobj, 'read_bytes'):
                                size = len(xobj.read_bytes())
                                print(f"  {name}: {subtype}, {size} bytes")
                            else:
                                print(f"  {name}: {subtype}")
                
                # 3. Check annotations
                if "/Annots" in page:
                    annots = page["/Annots"]
                    print(f"Annotations: {len(annots)}")
                else:
                    print("No annotations")
                
                # 4. Analyze content stream in detail
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    print(f"Total operations: {len(operations)}")
                    
                    # Count operation types
                    op_counts = {}
                    for operands, operator in operations:
                        op_str = str(operator)
                        op_counts[op_str] = op_counts.get(op_str, 0) + 1
                    
                    print(f"Operation types: {dict(sorted(op_counts.items()))}")
                    
                    # Look for any text operations
                    text_operations = []
                    for i, (operands, operator) in enumerate(operations):
                        op_str = str(operator)
                        if op_str in ["Tj", "TJ", "'", '"']:
                            text_operations.append((i, operands))
                    
                    print(f"Text operations: {len(text_operations)}")
                    if text_operations:
                        print("First few text operations:")
                        for i, operands in text_operations[:5]:
                            print(f"  Op {i}: {operands}")
                    
                    # Look for Do operations (XObject calls)
                    do_operations = []
                    for i, (operands, operator) in enumerate(operations):
                        if str(operator) == "Do":
                            do_operations.append((i, operands))
                    
                    print(f"Do operations: {len(do_operations)}")
                    for i, operands in do_operations:
                        print(f"  Op {i}: Do {operands}")
                    
                    # Look for transformation matrices
                    matrix_operations = []
                    for i, (operands, operator) in enumerate(operations):
                        if str(operator) == "cm":
                            matrix_operations.append((i, operands))
                    
                    print(f"Transformation matrices: {len(matrix_operations)}")
                    for i, operands in matrix_operations[:5]:
                        print(f"  Op {i}: cm {operands}")
                
                except Exception as e:
                    print(f"Error analyzing content stream: {e}")
            
            # 5. Check document-level features
            print(f"\n=== DOCUMENT LEVEL ===")
            print(f"Root keys: {list(pdf.Root.keys())}")
            
            if "/AcroForm" in pdf.Root:
                print("Document has AcroForm (form fields)")
            
            if "/OCProperties" in pdf.Root:
                print("Document has Optional Content (layers)")
            
            # 6. Try to extract all text from the page using a different method
            print(f"\n=== ALTERNATIVE TEXT EXTRACTION ===")
            try:
                # Try to get raw content
                if page_idx < len(pdf.pages):
                    page = pdf.pages[page_idx]
                    if "/Contents" in page:
                        contents = page["/Contents"]
                        if hasattr(contents, 'read_bytes'):
                            raw_content = contents.read_bytes()
                            content_text = raw_content.decode('utf-8', errors='ignore')
                            
                            # Search for any URL-like patterns in raw content
                            url_patterns = ["github", "https", "http", ".com", "chenyz"]
                            found_in_raw = []
                            for pattern in url_patterns:
                                if pattern.lower() in content_text.lower():
                                    found_in_raw.append(pattern)
                            
                            if found_in_raw:
                                print(f"Found in raw content: {found_in_raw}")
                            else:
                                print("No URL patterns found in raw content")
                            
                            # Show a sample of the raw content
                            print(f"Raw content sample (first 500 chars):")
                            print(repr(content_text[:500]))
                        
            except Exception as e:
                print(f"Error in alternative extraction: {e}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python final_reality_check.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    final_reality_check(pdf_file)
