#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced watermark remover based on new understanding:
- Watermarks are rotated text objects
- Each page has 4 independent text blocks
- Text content is Caesar cipher encoded
- Uses specific transformation matrices
"""
import sys
import pikepdf
import math

def is_watermark_matrix(matrix):
    """
    Check if a transformation matrix matches watermark patterns
    """
    if len(matrix) < 6:
        return False
    
    try:
        a, b, c, d, e, f = [float(x) for x in matrix[:6]]
        
        # Known watermark matrix pattern: [2.16507, 1.25, -1.25, 2.16507, 0, 0]
        if (abs(a - 2.16507) < 0.01 and abs(b - 1.25) < 0.01 and 
            abs(c + 1.25) < 0.01 and abs(d - 2.16507) < 0.01):
            return True
        
        # Check for other rotation/scaling patterns that might be watermarks
        # Calculate rotation angle and scale
        scale_x = math.sqrt(a*a + b*b)
        scale_y = math.sqrt(c*c + d*d)
        
        # If it's a significant rotation with scaling, might be watermark
        if scale_x > 1.5 and scale_y > 1.5:
            angle = math.atan2(b, a) * 180 / math.pi
            if abs(angle) > 30:  # Rotated more than 30 degrees
                return True
                
    except:
        pass
    
    return False

def is_watermark_text(text_content):
    """
    Check if text content matches watermark patterns
    """
    text_str = str(text_content).upper()
    
    # Direct encoded patterns
    encoded_patterns = ["KWWSV", "JLWKX", "FKHQ", "OXHQW", "WKRQ"]
    if any(pattern in text_str for pattern in encoded_patterns):
        return True
    
    # Decoded patterns (in case some are not encoded)
    decoded_patterns = ["HTTPS", "GITHUB", "CHEN", "FLUENT", "PYTHON"]
    if any(pattern in text_str for pattern in decoded_patterns):
        return True
    
    # URL patterns
    if "GITHUB.COM" in text_str or "FLUENTPYTHON" in text_str:
        return True
    
    return False

def analyze_text_block(text_block_ops):
    """
    Analyze a text block to determine if it's a watermark
    """
    has_watermark_matrix = False
    has_watermark_text = False
    
    for operands, operator in text_block_ops:
        op_str = str(operator)
        
        if op_str == "Tm":  # Text matrix
            if is_watermark_matrix(operands):
                has_watermark_matrix = True
        
        elif op_str in ["Tj", "TJ", "'", '"']:  # Text showing operations
            if is_watermark_text(operands):
                has_watermark_text = True
    
    # A watermark block should have both special matrix AND watermark text
    return has_watermark_matrix or has_watermark_text

def remove_watermarks_advanced(input_pdf, output_pdf):
    """
    Advanced watermark removal targeting rotated text objects
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    total_watermarks_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Handle multiple content streams
                    if "/Contents" in page:
                        contents = page["/Contents"]
                        page_watermarks_removed = 0
                        
                        if isinstance(contents, pikepdf.Array):
                            # Multiple content streams
                            new_contents = []
                            
                            for stream_idx, stream in enumerate(contents):
                                if hasattr(stream, 'read_bytes'):
                                    try:
                                        operations = list(pikepdf.parse_content_stream(stream, ""))
                                        new_operations, removed = process_operations(operations, page_num)
                                        
                                        if removed > 0:
                                            page_watermarks_removed += removed
                                            # Create new stream with modified operations
                                            new_content = pikepdf.unparse_content_stream(new_operations)
                                            new_stream = pikepdf.Stream(pdf, new_content)
                                            new_contents.append(new_stream)
                                        else:
                                            new_contents.append(stream)
                                            
                                    except Exception as e:
                                        print(f"  Page {page_num + 1}, Stream {stream_idx}: Error - {e}")
                                        new_contents.append(stream)
                                else:
                                    new_contents.append(stream)
                            
                            if page_watermarks_removed > 0:
                                page["/Contents"] = pikepdf.Array(new_contents)
                        
                        else:
                            # Single content stream
                            try:
                                operations = list(pikepdf.parse_content_stream(contents, ""))
                                new_operations, removed = process_operations(operations, page_num)
                                
                                if removed > 0:
                                    page_watermarks_removed = removed
                                    new_content = pikepdf.unparse_content_stream(new_operations)
                                    page["/Contents"] = pikepdf.Stream(pdf, new_content)
                                    
                            except Exception as e:
                                print(f"  Page {page_num + 1}: Error processing single stream - {e}")
                        
                        if page_watermarks_removed > 0:
                            pages_processed += 1
                            total_watermarks_removed += page_watermarks_removed
                            
                            if page_num < 20:  # Print details for first 20 pages
                                print(f"  Page {page_num + 1}: Removed {page_watermarks_removed} watermark text blocks")
                    
                    # Progress indicator
                    if (page_num + 1) % 100 == 0:
                        print(f"  Processed {page_num + 1} pages... (removed {total_watermarks_removed} watermarks so far)")
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages with watermarks: {pages_processed}")
            print(f"  Total watermark text blocks removed: {total_watermarks_removed}")
            print(f"  Average watermarks per page: {total_watermarks_removed/max(pages_processed,1):.1f}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def process_operations(operations, page_num):
    """
    Process operations list and remove watermark text blocks
    """
    new_operations = []
    watermarks_removed = 0
    
    i = 0
    while i < len(operations):
        operands, operator = operations[i]
        op_str = str(operator)
        
        if op_str == "BT":  # Begin text block
            # Collect entire text block
            text_block_ops = [(operands, operator)]
            i += 1
            
            # Read until ET (End text)
            while i < len(operations):
                operands, operator = operations[i]
                op_str = str(operator)
                text_block_ops.append((operands, operator))
                
                if op_str == "ET":  # End text block
                    break
                i += 1
            
            # Analyze if this is a watermark text block
            if analyze_text_block(text_block_ops):
                watermarks_removed += 1
                if page_num < 10:  # Debug info for first 10 pages
                    print(f"    Removing watermark text block with {len(text_block_ops)} operations")
                # Skip this text block (don't add to new_operations)
            else:
                # Keep this text block
                new_operations.extend(text_block_ops)
        
        else:
            # Non-text operations, keep them
            new_operations.append((operands, operator))
        
        i += 1
    
    return new_operations, watermarks_removed

def main():
    if len(sys.argv) != 3:
        print("Usage: python advanced_watermark_remover.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_watermarks_advanced(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Advanced watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
