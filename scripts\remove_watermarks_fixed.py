#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fixed PDF watermark remover - more precise approach
"""
import sys
import pikepdf

def remove_watermarks_precise(input_pdf, output_pdf):
    """
    Remove watermarks by:
    1. Identifying watermark XObjects (Form objects that contain large images)
    2. Removing Do calls to these XObjects from content streams
    3. Cleaning up unused XObject resources
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # First pass: identify all watermark XObjects
            watermark_xobjects = set()
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    resources = page.get("/Resources", {})
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        
                        for name, xobj in xobjects.items():
                            if hasattr(xobj, 'get'):
                                subtype = xobj.get("/Subtype", "Unknown")
                                
                                # Check if it's a Form XObject
                                if subtype == pikepdf.Name("/Form"):
                                    try:
                                        # Check if the form contains image references
                                        content = xobj.read_bytes().decode('utf-8', errors='ignore')
                                        if any(pattern in content for pattern in ["Do", "/X4", "/Image"]):
                                            watermark_xobjects.add(str(name))
                                            print(f"  Identified watermark XObject: {name} on page {page_num + 1}")
                                    except:
                                        pass
                                
                                # Also check large images (> 4MB likely watermarks)
                                elif subtype == pikepdf.Name("/Image"):
                                    try:
                                        size = len(xobj.read_bytes())
                                        if size > 4000000:  # > 4MB
                                            watermark_xobjects.add(str(name))
                                            print(f"  Identified large image watermark: {name} on page {page_num + 1}")
                                    except:
                                        pass
                                        
                except Exception as e:
                    print(f"Error analyzing page {page_num + 1}: {e}")
                    continue
            
            print(f"Total watermark XObjects identified: {len(watermark_xobjects)}")
            print(f"Watermark patterns: {sorted(list(watermark_xobjects))[:10]}...")  # Show first 10
            
            # Second pass: remove Do calls to watermark XObjects
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Parse content stream
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        # Check if this is a Do operation calling a watermark
                        if str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            # Debug print for first few pages
                            if page_num < 3:
                                print(f"  Page {page_num + 1}: Found Do operation: {xobj_name}")

                            if xobj_name in watermark_xobjects:
                                operations_removed += 1
                                page_modified = True
                                if page_num < 10:  # Only print for first 10 pages to avoid spam
                                    print(f"  Page {page_num + 1}: Removing Do {xobj_name}")
                                continue

                        # Keep all other operations
                        new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Third pass: clean up unused XObject resources
            print("Cleaning up unused XObject resources...")
            resources_cleaned = 0
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    resources = page.get("/Resources", {})
                    if "/XObject" in resources:
                        xobjects = dict(resources["/XObject"])
                        original_count = len(xobjects)
                        
                        # Remove watermark XObjects from resources
                        for watermark_name in watermark_xobjects:
                            if watermark_name in xobjects:
                                del xobjects[watermark_name]
                        
                        # Update resources if anything was removed
                        if len(xobjects) < original_count:
                            if xobjects:
                                resources["/XObject"] = pikepdf.Dictionary(xobjects)
                            else:
                                del resources["/XObject"]
                            page["/Resources"] = resources
                            resources_cleaned += 1
                            
                except Exception as e:
                    print(f"Error cleaning resources on page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Watermark XObjects identified: {len(watermark_xobjects)}")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Do operations removed: {operations_removed}")
            print(f"  Pages with resources cleaned: {resources_cleaned}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_watermarks_fixed.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_watermarks_precise(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
