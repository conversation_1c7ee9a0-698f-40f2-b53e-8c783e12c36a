#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to examine the actual content streams
"""
import sys
import pikepdf

def debug_page_content(pdf_path, page_num=1):
    """
    Debug the content stream of a specific page
    """
    print(f"Debugging page {page_num} of {pdf_path}")
    print("=" * 60)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            if page_num > len(pdf.pages):
                print(f"Error: Page {page_num} doesn't exist (total: {len(pdf.pages)})")
                return
            
            page = pdf.pages[page_num - 1]  # 0-based index
            
            # Show resources
            resources = page.get("/Resources", {})
            print(f"Resources: {list(resources.keys())}")
            
            if "/XObject" in resources:
                xobjects = resources["/XObject"]
                print(f"XObjects: {list(xobjects.keys())}")
            
            # Parse and show content stream operations
            operations = list(pikepdf.parse_content_stream(page, ""))
            print(f"Total operations: {len(operations)}")
            
            # Look for Do operations
            do_operations = []
            for i, (operands, operator) in enumerate(operations):
                if str(operator) == "Do":
                    do_operations.append((i, operands, operator))
            
            print(f"Do operations found: {len(do_operations)}")
            for i, operands, operator in do_operations:
                print(f"  Operation {i}: {operator} {operands}")
            
            # Show first 20 operations for context
            print("\nFirst 20 operations:")
            for i, (operands, operator) in enumerate(operations[:20]):
                print(f"  {i:3d}: {operator} {operands}")
            
            # Show last 20 operations
            if len(operations) > 20:
                print(f"\nLast 20 operations:")
                for i, (operands, operator) in enumerate(operations[-20:], len(operations)-20):
                    print(f"  {i:3d}: {operator} {operands}")
                    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python debug_content.py <pdf_file> [page_number]")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    page_num = int(sys.argv[2]) if len(sys.argv) > 2 else 1
    
    debug_page_content(pdf_file, page_num)
