#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final cleanup - remove remaining Do operations that reference non-existent XObjects
"""
import sys
import pikepdf

def final_cleanup(input_pdf, output_pdf):
    """
    Remove Do operations that reference XObjects that no longer exist
    """
    print(f"Final cleanup: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Get available XObjects in resources
                    resources = page.get("/Resources", {})
                    available_xobjects = set()
                    
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        available_xobjects = set(str(name) for name in xobjects.keys())
                    
                    # Process content stream
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        should_remove = False
                        
                        # Check Do operations
                        if str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            
                            # If the XObject doesn't exist in resources, remove the Do operation
                            if xobj_name not in available_xobjects:
                                should_remove = True
                                operations_removed += 1
                                page_modified = True
                                
                                if page_num < 5:  # Print for first 5 pages
                                    print(f"  Page {page_num + 1}: Removing orphaned Do {xobj_name}")
                            
                            # Also remove known watermark XObject calls
                            elif any(pattern in xobj_name for pattern in ["/Fm", "/X4", "/Image1"]):
                                should_remove = True
                                operations_removed += 1
                                page_modified = True
                                
                                if page_num < 5:
                                    print(f"  Page {page_num + 1}: Removing watermark Do {xobj_name}")
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Do operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python final_cleanup.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not final_cleanup(input_pdf, output_pdf):
        print("Failed to perform final cleanup")
        sys.exit(1)
    
    print(f"\n🎉 Final cleanup completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
