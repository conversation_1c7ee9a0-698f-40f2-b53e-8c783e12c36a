#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test if watermarks can still be found via text search
"""
import sys
import pikepdf

def test_watermark_search(pdf_path):
    """
    Test if watermark text can be found in the PDF
    """
    print(f"Testing watermark search in: {pdf_path}")
    print("=" * 60)
    
    search_patterns = [
        "github.com",
        "chenyz1984",
        "FluentPython",
        "https://",
        "KWWSV",
        "JLWKX",
        "FKHQ"
    ]
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            found_patterns = {}
            total_matches = 0
            
            # Check first 20 pages and some random pages
            pages_to_check = list(range(20)) + [50, 100, 200, 300, 500, 700]
            pages_to_check = [p for p in pages_to_check if p < len(pdf.pages)]
            
            for page_idx in pages_to_check:
                page = pdf.pages[page_idx]
                page_matches = []
                
                try:
                    # Get all text operations
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    
                    for operands, operator in operations:
                        if str(operator) in ["Tj", "TJ", "'", '"']:
                            text_content = str(operands).upper()
                            
                            for pattern in search_patterns:
                                if pattern.upper() in text_content:
                                    page_matches.append((pattern, text_content[:100]))
                                    total_matches += 1
                    
                    if page_matches:
                        print(f"Page {page_idx + 1}: Found {len(page_matches)} matches")
                        for pattern, content in page_matches[:3]:  # Show first 3
                            print(f"  {pattern}: {content}...")
                        
                        for pattern, _ in page_matches:
                            found_patterns[pattern] = found_patterns.get(pattern, 0) + 1
                
                except Exception as e:
                    print(f"Page {page_idx + 1}: Error - {e}")
            
            print(f"\nSummary:")
            print(f"Total matches found: {total_matches}")
            print(f"Pattern distribution:")
            for pattern, count in sorted(found_patterns.items()):
                print(f"  {pattern}: {count} occurrences")
            
            if total_matches == 0:
                print("✅ No watermark patterns found - PDF appears clean!")
            else:
                print("❌ Watermark patterns still present")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_watermark_search.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    test_watermark_search(pdf_file)
