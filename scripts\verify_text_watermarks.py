#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verify text watermark removal
"""
import sys
import pikepdf

def verify_text_watermarks(pdf_path):
    """
    Check for text-based watermarks in the PDF
    """
    print(f"Verifying text watermarks in: {pdf_path}")
    print("=" * 60)
    
    text_watermarks_found = 0
    pages_with_text_watermarks = 0
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check first 10 pages for text watermarks
            for page_num in range(min(10, len(pdf.pages))):
                page = pdf.pages[page_num]
                page_has_text_watermark = False
                
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    
                    for i, (operands, operator) in enumerate(operations):
                        if str(operator) == "TJ":
                            try:
                                operands_str = str(operands)
                                
                                # Check for encoded watermark patterns
                                watermark_patterns = [
                                    "KWWSV",  # <PERSON><PERSON><PERSON> encoded
                                    "<PERSON><PERSON>WK<PERSON>",  # <PERSON><PERSON>HUB encoded
                                    "FKHQ",   # <PERSON>EN encoded
                                    "OXHQW",  # FLUENT encoded
                                    "WKRQ",   # PYTHON encoded
                                ]
                                
                                for pattern in watermark_patterns:
                                    if pattern in operands_str:
                                        text_watermarks_found += 1
                                        page_has_text_watermark = True
                                        print(f"  Page {page_num + 1}: ❌ Found text watermark pattern '{pattern}' in operation {i}")
                                        break
                                        
                            except:
                                pass
                
                except Exception as e:
                    print(f"  Page {page_num + 1}: Error checking - {e}")
                
                if page_has_text_watermark:
                    pages_with_text_watermarks += 1
                else:
                    print(f"  Page {page_num + 1}: ✅ No text watermarks detected")
            
            print(f"\nSummary:")
            print(f"  Pages checked: {min(10, len(pdf.pages))}")
            print(f"  Pages with text watermarks: {pages_with_text_watermarks}")
            print(f"  Total text watermarks found: {text_watermarks_found}")
            
            if text_watermarks_found == 0:
                print("  🎉 No text watermarks detected!")
                return True
            else:
                print("  ❌ Text watermarks still present")
                return False
                
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python verify_text_watermarks.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    success = verify_text_watermarks(pdf_file)
    
    if success:
        print("\n✅ Verification successful! No text watermarks found.")
    else:
        print("\n⚠️  Verification failed. Text watermarks still present.")
