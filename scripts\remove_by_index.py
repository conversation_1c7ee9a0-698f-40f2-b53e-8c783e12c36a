#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Remove watermarks by operation index (we know operations 84, 142, 200, 258 on page 1)
"""
import sys
import pikepdf

def remove_watermarks_by_index(input_pdf, output_pdf):
    """
    Remove watermarks by targeting specific operation indices
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    # Find and remove TJ operations that contain watermark patterns
                    for i, (operands, operator) in enumerate(operations):
                        should_remove = False
                        
                        if str(operator) == "TJ":
                            try:
                                # Check if this TJ operation contains watermark content
                                if operands and len(operands) > 0:
                                    # Get the first operand (should be an Array)
                                    first_operand = operands[0]
                                    
                                    # Convert to string and check for watermark patterns
                                    operand_str = str(first_operand)
                                    
                                    # Look for the specific patterns we found
                                    watermark_indicators = [
                                        "KWWSV",  # HTTPS encoded
                                        "JLWKX",  # GITHUB encoded
                                        "FKHQ",   # CHEN encoded
                                        "OXHQW",  # FLUENT encoded
                                        "WKRQ",   # PYTHON encoded
                                    ]
                                    
                                    if any(indicator in operand_str for indicator in watermark_indicators):
                                        should_remove = True
                                        operations_removed += 1
                                        page_modified = True
                                        
                                        if page_num < 5:  # Print for first 5 pages
                                            print(f"  Page {page_num + 1}, Operation {i}: Removing TJ watermark")
                                            print(f"    Content: {operand_str[:100]}...")
                                
                            except Exception as e:
                                pass
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                        if page_num < 5:
                            print(f"  Page {page_num + 1}: Updated content stream ({len(operations)} -> {len(new_operations)} operations)")
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Text operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_by_index.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_watermarks_by_index(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Watermark removal by index completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
