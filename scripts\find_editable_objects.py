#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Find objects that can be edited in PDF editors (like 福昕's "编辑对象" feature)
"""
import sys
import pikepdf

def find_editable_objects(pdf_path):
    """
    Find objects that PDF editors can select and edit
    """
    print(f"Searching for editable objects in: {pdf_path}")
    print("=" * 80)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check first few pages in detail
            for page_idx in range(min(5, len(pdf.pages))):
                page = pdf.pages[page_idx]
                print(f"\n{'='*20} PAGE {page_idx + 1} {'='*20}")
                
                # 1. Check for annotations (these are often editable)
                if "/Annots" in page:
                    annots = page["/Annots"]
                    print(f"Annotations found: {len(annots)}")
                    
                    for i, annot in enumerate(annots):
                        try:
                            subtype = annot.get("/Subtype", "Unknown")
                            contents = annot.get("/Contents", "")
                            rect = annot.get("/Rect", "")
                            
                            print(f"  Annotation {i}:")
                            print(f"    Type: {subtype}")
                            print(f"    Contents: {contents}")
                            print(f"    Rectangle: {rect}")
                            
                            # Check all properties for watermark content
                            for key in annot.keys():
                                try:
                                    value = str(annot[key])
                                    if any(pattern in value.lower() for pattern in [
                                        "github", "chenyz", "fluent", "python", "https"
                                    ]):
                                        print(f"    *** WATERMARK CONTENT in {key}: {value} ***")
                                except:
                                    pass
                                    
                        except Exception as e:
                            print(f"  Error reading annotation {i}: {e}")
                else:
                    print("No annotations found")
                
                # 2. Look for text objects that might be watermarks
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    print(f"Total operations: {len(operations)}")
                    
                    # Group operations by text blocks
                    text_blocks = []
                    current_block = []
                    in_text_block = False
                    
                    for i, (operands, operator) in enumerate(operations):
                        op_str = str(operator)
                        
                        if op_str == "BT":  # Begin text
                            in_text_block = True
                            current_block = [i]
                        elif op_str == "ET":  # End text
                            if in_text_block:
                                current_block.append(i)
                                text_blocks.append(current_block)
                                current_block = []
                            in_text_block = False
                        elif in_text_block:
                            current_block.append(i)
                    
                    print(f"Text blocks found: {len(text_blocks)}")
                    
                    # Analyze each text block
                    for block_idx, block_ops in enumerate(text_blocks):
                        print(f"  Text block {block_idx}:")
                        
                        block_text = []
                        has_positioning = False
                        
                        for op_idx in block_ops:
                            if op_idx < len(operations):
                                operands, operator = operations[op_idx]
                                op_str = str(operator)
                                
                                if op_str in ["Tj", "TJ", "'", '"']:
                                    # Text showing operations
                                    text_content = str(operands)
                                    block_text.append(text_content)
                                    
                                    # Check for watermark patterns
                                    if any(pattern in text_content.lower() for pattern in [
                                        "github", "chenyz", "fluent", "python", "https", "kwwsv", "jlwkx"
                                    ]):
                                        print(f"    *** POTENTIAL WATERMARK TEXT: {text_content} ***")
                                
                                elif op_str in ["Tm", "Td", "TD"]:
                                    # Text positioning
                                    has_positioning = True
                                    print(f"    Position: {op_str} {operands}")
                        
                        if block_text:
                            combined_text = " ".join(block_text)
                            print(f"    Combined text: {combined_text[:100]}...")
                            
                            # Check if this could be a watermark
                            if any(pattern in combined_text.lower() for pattern in [
                                "github", "chenyz", "fluent", "python", "https"
                            ]):
                                print(f"    *** THIS BLOCK CONTAINS WATERMARK CONTENT ***")
                
                except Exception as e:
                    print(f"Error analyzing text operations: {e}")
                
                # 3. Check for form fields (these are also editable)
                if "/AcroForm" in pdf.Root:
                    print("Document has form fields")
                    acroform = pdf.Root["/AcroForm"]
                    if "/Fields" in acroform:
                        fields = acroform["/Fields"]
                        print(f"Form fields: {len(fields)}")
                        
                        for i, field in enumerate(fields):
                            try:
                                field_type = field.get("/FT", "Unknown")
                                field_value = field.get("/V", "")
                                print(f"  Field {i}: Type={field_type}, Value={field_value}")
                                
                                if any(pattern in str(field_value).lower() for pattern in [
                                    "github", "chenyz", "fluent"
                                ]):
                                    print(f"    *** WATERMARK in form field ***")
                            except:
                                pass
                else:
                    print("No form fields found")
                
                # 4. Look for graphics state and transformation matrices
                print("\nGraphics operations analysis:")
                graphics_ops = []
                for i, (operands, operator) in enumerate(operations):
                    op_str = str(operator)
                    if op_str in ["q", "Q", "cm", "gs"]:  # Graphics state operations
                        graphics_ops.append((i, op_str, operands))
                
                print(f"Graphics state operations: {len(graphics_ops)}")
                if len(graphics_ops) > 20:  # Show first 20
                    for i, op_str, operands in graphics_ops[:20]:
                        print(f"  Op {i}: {op_str} {operands}")
                    print(f"  ... and {len(graphics_ops) - 20} more")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python find_editable_objects.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    find_editable_objects(pdf_file)
