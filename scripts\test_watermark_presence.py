#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test if watermarks are actually present in processed PDFs
"""
import sys
import pikepdf

def test_watermark_presence(pdf_path):
    """
    Test if watermarks are still present in the PDF
    """
    print(f"Testing watermark presence in: {pdf_path}")
    print("=" * 60)
    
    watermark_found = False
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Test first page in detail
            page = pdf.pages[0]
            print(f"\nPage 1 analysis:")
            
            # Check operations
            operations = list(pikepdf.parse_content_stream(page, ""))
            print(f"Total operations: {len(operations)}")
            
            # Look for TJ operations with watermark content
            tj_operations = []
            for i, (operands, operator) in enumerate(operations):
                if str(operator) == "TJ":
                    tj_operations.append((i, operands))
            
            print(f"TJ operations found: {len(tj_operations)}")
            
            # Check each TJ operation for watermark patterns
            watermark_patterns = ["KWWSV", "JLWKX", "FKHQ", "OXHQW", "WKRQ"]
            
            for i, operands in tj_operations:
                operands_str = str(operands)
                print(f"  TJ operation {i}: {operands_str[:100]}...")
                
                for pattern in watermark_patterns:
                    if pattern in operands_str:
                        print(f"    *** WATERMARK PATTERN FOUND: {pattern} ***")
                        watermark_found = True
            
            # Also check if we can find the exact watermark text
            full_watermark = "KWWSV˛JLWKXEFRPFKHQ\\]˝˙)OXHQW3\\WKRQQG&1"
            
            for i, (operands, operator) in enumerate(operations):
                if str(operator) == "TJ":
                    operands_str = str(operands)
                    if full_watermark in operands_str:
                        print(f"    *** FULL WATERMARK FOUND in operation {i} ***")
                        watermark_found = True
            
            # Check Do operations
            do_operations = []
            for i, (operands, operator) in enumerate(operations):
                if str(operator) == "Do":
                    do_operations.append((i, operands))
            
            print(f"Do operations found: {len(do_operations)}")
            for i, operands in do_operations:
                print(f"  Do operation {i}: {operands}")
            
            # Check resources
            resources = page.get("/Resources", {})
            print(f"Resources: {list(resources.keys())}")
            
            if "/XObject" in resources:
                xobjects = resources["/XObject"]
                print(f"XObjects: {list(xobjects.keys())}")
            else:
                print("No XObjects in resources")
                
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return not watermark_found

def compare_pdfs(original_pdf, processed_pdf):
    """
    Compare original and processed PDFs
    """
    print(f"\nComparing PDFs:")
    print(f"Original: {original_pdf}")
    print(f"Processed: {processed_pdf}")
    print("=" * 60)
    
    try:
        with pikepdf.Pdf.open(original_pdf) as orig, pikepdf.Pdf.open(processed_pdf) as proc:
            # Compare first page
            orig_ops = list(pikepdf.parse_content_stream(orig.pages[0], ""))
            proc_ops = list(pikepdf.parse_content_stream(proc.pages[0], ""))
            
            print(f"Original page 1 operations: {len(orig_ops)}")
            print(f"Processed page 1 operations: {len(proc_ops)}")
            print(f"Difference: {len(orig_ops) - len(proc_ops)} operations")
            
            # Count TJ operations
            orig_tj = sum(1 for _, op in orig_ops if str(op) == "TJ")
            proc_tj = sum(1 for _, op in proc_ops if str(op) == "TJ")
            
            print(f"Original TJ operations: {orig_tj}")
            print(f"Processed TJ operations: {proc_tj}")
            print(f"TJ operations removed: {orig_tj - proc_tj}")
            
            # Count Do operations
            orig_do = sum(1 for _, op in orig_ops if str(op) == "Do")
            proc_do = sum(1 for _, op in proc_ops if str(op) == "Do")
            
            print(f"Original Do operations: {orig_do}")
            print(f"Processed Do operations: {proc_do}")
            print(f"Do operations removed: {orig_do - proc_do}")
            
    except Exception as e:
        print(f"Error comparing PDFs: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_watermark_presence.py <pdf_file> [original_pdf]")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    success = test_watermark_presence(pdf_file)
    
    if len(sys.argv) >= 3:
        original_pdf = sys.argv[2]
        compare_pdfs(original_pdf, pdf_file)
    
    if success:
        print("\n✅ No watermarks detected!")
    else:
        print("\n❌ Watermarks still present!")
