#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple PDF watermark remover for FluentPython PDF
Based on analysis, the watermark is implemented as:
- /Fm0 Form XObject that calls /X4 Image XObject
- Each page has a "Do /Fm0" operation that renders the watermark
"""
import sys
import pikepdf

def remove_watermark_form_xobjects(input_pdf, output_pdf):
    """
    Remove watermark by eliminating the Do /Fm0 operations from each page
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Parse the content stream
                    operations = pikepdf.parse_content_stream(page, "")
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        # Skip "Do /Fm0" operations (watermark calls)
                        if (str(operator) == "Do" and 
                            operands and 
                            str(operands[0]) == "/Fm0"):
                            operations_removed += 1
                            page_modified = True
                            print(f"  Page {page_num + 1}: Removed Do /Fm0 operation")
                            continue
                        
                        # Keep all other operations
                        new_operations.append((operands, operator))
                    
                    # Update the page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the modified PDF
            pdf.save(output_pdf)
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def clean_unused_resources(input_pdf, output_pdf):
    """
    Additional cleanup: remove unused XObjects (/Fm0, /X4) from resources
    """
    print(f"\nCleaning unused resources...")
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            resources_cleaned = 0
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    resources = page.get("/Resources", {})
                    if "/XObject" in resources:
                        xobjects = dict(resources["/XObject"])
                        
                        # Remove /Fm0 and /X4 if they exist
                        removed = []
                        if "/Fm0" in xobjects:
                            del xobjects["/Fm0"]
                            removed.append("/Fm0")
                        if "/X4" in xobjects:
                            del xobjects["/X4"]
                            removed.append("/X4")
                        
                        if removed:
                            # Update the resources
                            if xobjects:
                                resources["/XObject"] = pikepdf.Dictionary(xobjects)
                            else:
                                del resources["/XObject"]
                            page["/Resources"] = resources
                            resources_cleaned += 1
                            print(f"  Page {page_num + 1}: Removed {', '.join(removed)}")
                            
                except Exception as e:
                    print(f"Error cleaning page {page_num + 1}: {e}")
                    continue
            
            pdf.save(output_pdf)
            print(f"  Resources cleaned on {resources_cleaned} pages")
            
    except Exception as e:
        print(f"Error during cleanup: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_watermark_simple.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    # Step 1: Remove Do /Fm0 operations
    temp_pdf = output_pdf + ".temp"
    if not remove_watermark_form_xobjects(input_pdf, temp_pdf):
        print("Failed to remove watermark operations")
        sys.exit(1)
    
    # Step 2: Clean unused resources
    if not clean_unused_resources(temp_pdf, output_pdf):
        print("Failed to clean resources")
        sys.exit(1)
    
    # Remove temp file
    import os
    try:
        os.remove(temp_pdf)
    except:
        pass
    
    print(f"\nWatermark removal completed successfully!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
