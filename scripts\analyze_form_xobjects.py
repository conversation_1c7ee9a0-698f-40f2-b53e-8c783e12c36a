#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyze Form XObjects to understand watermark structure
"""
import sys
import pikepdf

def analyze_form_xobjects(pdf_path):
    """
    Analyze Form XObjects in detail
    """
    print(f"Analyzing Form XObjects in: {pdf_path}")
    print("=" * 80)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check first few pages for Form XObjects
            for page_idx in range(min(10, len(pdf.pages))):
                page = pdf.pages[page_idx]
                print(f"\n--- PAGE {page_idx + 1} ---")
                
                if "/Resources" in page:
                    resources = page["/Resources"]
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        
                        for name, xobj in xobjects.items():
                            try:
                                subtype = xobj.get("/Subtype", "Unknown")
                                print(f"{name}: {subtype}")
                                
                                if subtype == pikepdf.Name("/Form"):
                                    print(f"  Analyzing Form XObject {name}:")
                                    
                                    # Check Form XObject properties
                                    for key in xobj.keys():
                                        if key != "/Length":  # Skip length
                                            print(f"    {key}: {xobj[key]}")
                                    
                                    # Read Form XObject content
                                    if hasattr(xobj, 'read_bytes'):
                                        try:
                                            form_content = xobj.read_bytes()
                                            form_text = form_content.decode('utf-8', errors='ignore')
                                            print(f"    Content length: {len(form_text)} chars")
                                            print(f"    Content preview: {form_text[:500]}...")
                                            
                                            # Parse Form XObject operations
                                            try:
                                                form_operations = list(pikepdf.parse_content_stream(xobj, ""))
                                                print(f"    Operations: {len(form_operations)}")
                                                
                                                # Look for text operations
                                                text_ops = []
                                                do_ops = []
                                                
                                                for i, (operands, operator) in enumerate(form_operations):
                                                    op_str = str(operator)
                                                    if op_str in ["TJ", "Tj"]:
                                                        text_ops.append((i, operands))
                                                    elif op_str == "Do":
                                                        do_ops.append((i, operands))
                                                
                                                print(f"    Text operations: {len(text_ops)}")
                                                for i, operands in text_ops:
                                                    print(f"      Op {i}: {operands}")
                                                
                                                print(f"    Do operations: {len(do_ops)}")
                                                for i, operands in do_ops:
                                                    print(f"      Op {i}: {operands}")
                                                    
                                                    # Check if this Do operation references another XObject
                                                    if operands:
                                                        referenced_xobj = str(operands[0])
                                                        print(f"        References: {referenced_xobj}")
                                                        
                                                        # Try to find this referenced XObject
                                                        if "/Resources" in xobj:
                                                            form_resources = xobj["/Resources"]
                                                            if "/XObject" in form_resources:
                                                                form_xobjects = form_resources["/XObject"]
                                                                if referenced_xobj in form_xobjects:
                                                                    ref_xobj = form_xobjects[referenced_xobj]
                                                                    ref_subtype = ref_xobj.get("/Subtype", "Unknown")
                                                                    print(f"          Found {referenced_xobj}: {ref_subtype}")
                                                                    
                                                                    if hasattr(ref_xobj, 'read_bytes'):
                                                                        try:
                                                                            ref_content = ref_xobj.read_bytes()
                                                                            ref_text = ref_content.decode('utf-8', errors='ignore')
                                                                            
                                                                            # Check for watermark patterns
                                                                            patterns = ["github", "chenyz", "KWWSV", "JLWKX", "FluentPython"]
                                                                            found_patterns = [p for p in patterns if p in ref_text]
                                                                            
                                                                            if found_patterns:
                                                                                print(f"          *** WATERMARK PATTERNS: {found_patterns} ***")
                                                                                print(f"          Content: {ref_text[:200]}...")
                                                                            else:
                                                                                print(f"          Content size: {len(ref_content)} bytes")
                                                                        except Exception as e:
                                                                            print(f"          Error reading content: {e}")
                                                
                                            except Exception as e:
                                                print(f"    Error parsing operations: {e}")
                                                
                                        except Exception as e:
                                            print(f"    Error reading content: {e}")
                                
                                elif subtype == pikepdf.Name("/Image"):
                                    print(f"  Image XObject {name}: {len(xobj.read_bytes()) if hasattr(xobj, 'read_bytes') else 'N/A'} bytes")
                                    
                            except Exception as e:
                                print(f"  Error analyzing {name}: {e}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python analyze_form_xobjects.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    analyze_form_xobjects(pdf_file)
