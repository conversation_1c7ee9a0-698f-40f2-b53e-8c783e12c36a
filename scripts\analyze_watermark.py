#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple PDF watermark analyzer to understand the structure
"""
import sys
import pikepdf

def analyze_pdf_structure(pdf_path, pattern="https://github.com/chenyz1984/FluentPython2ndCN"):
    """Analyze PDF structure to understand how watermarks are implemented"""
    
    print(f"Analyzing PDF: {pdf_path}")
    print(f"Looking for pattern: {pattern}")
    print("=" * 60)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Analyze first few pages to understand structure
            for page_num in range(min(5, len(pdf.pages))):
                page = pdf.pages[page_num]
                print(f"\n--- Page {page_num + 1} ---")
                
                # Check page resources
                try:
                    resources = page.get("/Resources", {})
                    print(f"Resources keys: {list(resources.keys())}")
                    
                    # Check XObjects
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        print(f"XObjects: {list(xobjects.keys())}")
                        
                        # Recursively analyze all XObjects
                        def analyze_xobject(name, xobj, level=0):
                            indent = "  " * (level + 1)
                            if hasattr(xobj, 'get'):
                                subtype = xobj.get("/Subtype", "Unknown")
                                print(f"{indent}{name}: Subtype={subtype}")

                                # Try to read stream content
                                if hasattr(xobj, 'read_bytes'):
                                    try:
                                        content = xobj.read_bytes()
                                        print(f"{indent}  Stream size: {len(content)} bytes")

                                        # Show a snippet of the content
                                        snippet = content[:500].decode('utf-8', errors='ignore')
                                        print(f"{indent}  Content snippet: {snippet}")

                                        # Check if it contains the pattern in any form
                                        content_str = content.decode('utf-8', errors='ignore')
                                        if pattern in content_str:
                                            print(f"{indent}  *** PATTERN FOUND AS TEXT in {name} ***")
                                        if "github" in content_str.lower():
                                            print(f"{indent}  *** 'github' found in {name} ***")
                                        if "chenyz1984" in content_str.lower():
                                            print(f"{indent}  *** 'chenyz1984' found in {name} ***")

                                        # Check if this XObject has its own resources
                                        if hasattr(xobj, 'get') and "/Resources" in xobj:
                                            xobj_resources = xobj["/Resources"]
                                            if "/XObject" in xobj_resources:
                                                nested_xobjs = xobj_resources["/XObject"]
                                                print(f"{indent}  Nested XObjects: {list(nested_xobjs.keys())}")
                                                for nested_name, nested_xobj in nested_xobjs.items():
                                                    analyze_xobject(nested_name, nested_xobj, level + 1)

                                    except Exception as e:
                                        print(f"{indent}  Could not read content: {e}")

                        for name, xobj in xobjects.items():
                            analyze_xobject(name, xobj)
                    
                    # Check annotations
                    if "/Annots" in page:
                        annots = page["/Annots"]
                        print(f"Annotations: {len(annots)} found")
                        for i, annot in enumerate(annots):
                            if hasattr(annot, 'get'):
                                subtype = annot.get("/Subtype", "Unknown")
                                contents = annot.get("/Contents", "")
                                print(f"  Annotation {i}: Subtype={subtype}")
                                if contents and pattern.lower() in str(contents).lower():
                                    print(f"    *** FOUND PATTERN in annotation ***")
                                    print(f"    Contents: {contents}")
                
                except Exception as e:
                    print(f"Error analyzing page {page_num + 1}: {e}")
                
                # Try to parse content stream
                try:
                    print("Parsing content stream...")
                    operations = pikepdf.parse_content_stream(page, "")
                    
                    text_ops = 0
                    do_ops = 0
                    pattern_found = 0
                    
                    for operands, operator in operations:
                        op_str = str(operator)
                        if op_str in ['Tj', 'TJ', "'", '"']:
                            text_ops += 1
                            # Try to extract text
                            try:
                                if operands:
                                    text = str(operands[0])
                                    if pattern.lower() in text.lower():
                                        pattern_found += 1
                                        print(f"    *** FOUND PATTERN in text operation: {text[:100]} ***")
                            except:
                                pass
                        elif op_str == 'Do':
                            do_ops += 1
                            if operands:
                                print(f"    Do operation calls: {operands[0]}")
                    
                    print(f"  Text operations: {text_ops}")
                    print(f"  Do operations: {do_ops}")
                    print(f"  Pattern matches in text: {pattern_found}")
                    
                except Exception as e:
                    print(f"Error parsing content stream: {e}")
                
                # Continue analyzing more pages
                    
    except Exception as e:
        print(f"Error opening PDF: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python analyze_watermark.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    analyze_pdf_structure(pdf_file)
