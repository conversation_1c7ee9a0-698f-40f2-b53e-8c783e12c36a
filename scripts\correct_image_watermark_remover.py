#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correct watermark remover - remove large image XObjects that are watermarks
Based on final discovery: watermarks are large image XObjects (3-5MB each)
"""
import sys
import pikepdf

def is_watermark_image(xobj):
    """
    Determine if an XObject is a watermark based on size and type
    """
    try:
        subtype = xobj.get("/Subtype", "Unknown")
        
        if subtype == pikepdf.Name("/Image"):
            if hasattr(xobj, 'read_bytes'):
                size = len(xobj.read_bytes())
                # Watermark images are typically 3-5MB
                if size > 3000000:  # > 3MB
                    return True
        
    except Exception:
        pass
    
    return False

def remove_image_watermarks(input_pdf, output_pdf):
    """
    Remove watermark images from all pages
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    total_watermarks_removed = 0
    total_size_saved = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_watermarks_removed = 0
                    page_size_saved = 0
                    
                    # Check for XObjects in page resources
                    if "/Resources" in page:
                        resources = page["/Resources"]
                        if "/XObject" in resources:
                            xobjects = dict(resources["/XObject"])
                            original_count = len(xobjects)
                            
                            # Identify and remove watermark images
                            watermark_names = []
                            for name, xobj in list(xobjects.items()):
                                if is_watermark_image(xobj):
                                    try:
                                        size = len(xobj.read_bytes())
                                        page_size_saved += size
                                        watermark_names.append(str(name))
                                        del xobjects[str(name)]
                                        page_watermarks_removed += 1
                                    except Exception:
                                        pass
                            
                            # Update page resources
                            if page_watermarks_removed > 0:
                                if xobjects:
                                    resources["/XObject"] = pikepdf.Dictionary(xobjects)
                                else:
                                    del resources["/XObject"]
                                page["/Resources"] = resources
                                
                                # Remove Do operations that call these watermark images
                                removed_do_ops = remove_do_operations(page, watermark_names, pdf)
                                
                                if page_num < 20:  # Print details for first 20 pages
                                    print(f"  Page {page_num + 1}: Removed {page_watermarks_removed} watermark images ({page_size_saved/1024/1024:.1f}MB)")
                                    if watermark_names:
                                        print(f"    Images: {watermark_names}")
                                    if removed_do_ops > 0:
                                        print(f"    Removed {removed_do_ops} Do operations")
                    
                    if page_watermarks_removed > 0:
                        pages_processed += 1
                        total_watermarks_removed += page_watermarks_removed
                        total_size_saved += page_size_saved
                    
                    # Progress indicator
                    if (page_num + 1) % 100 == 0:
                        print(f"  Processed {page_num + 1} pages... (removed {total_watermarks_removed} watermarks, saved {total_size_saved/1024/1024:.1f}MB)")
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages with watermarks: {pages_processed}")
            print(f"  Total watermark images removed: {total_watermarks_removed}")
            print(f"  Total size saved: {total_size_saved/1024/1024:.1f}MB")
            if pages_processed > 0:
                print(f"  Average watermarks per page: {total_watermarks_removed/pages_processed:.1f}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def remove_do_operations(page, watermark_names, pdf):
    """
    Remove Do operations that call watermark images
    """
    removed_count = 0
    
    try:
        if "/Contents" in page:
            contents = page["/Contents"]
            
            if isinstance(contents, pikepdf.Array):
                # Multiple content streams
                new_contents = []
                
                for stream in contents:
                    if hasattr(stream, 'read_bytes'):
                        try:
                            operations = list(pikepdf.parse_content_stream(stream, ""))
                            new_operations = []
                            
                            for operands, operator in operations:
                                should_remove = False
                                
                                if str(operator) == "Do" and operands:
                                    xobj_name = str(operands[0])
                                    if xobj_name in watermark_names:
                                        should_remove = True
                                        removed_count += 1
                                
                                if not should_remove:
                                    new_operations.append((operands, operator))
                            
                            # Update stream
                            new_content = pikepdf.unparse_content_stream(new_operations)
                            new_stream = pikepdf.Stream(pdf, new_content)
                            new_contents.append(new_stream)
                            
                        except Exception:
                            new_contents.append(stream)
                    else:
                        new_contents.append(stream)
                
                page["/Contents"] = pikepdf.Array(new_contents)
            
            else:
                # Single content stream
                try:
                    operations = list(pikepdf.parse_content_stream(contents, ""))
                    new_operations = []
                    
                    for operands, operator in operations:
                        should_remove = False
                        
                        if str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            if xobj_name in watermark_names:
                                should_remove = True
                                removed_count += 1
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update content
                    new_content = pikepdf.unparse_content_stream(new_operations)
                    page["/Contents"] = pikepdf.Stream(pdf, new_content)
                    
                except Exception:
                    pass
    
    except Exception:
        pass
    
    return removed_count

def main():
    if len(sys.argv) != 3:
        print("Usage: python correct_image_watermark_remover.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_image_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Image watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
