#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive scan to find ALL watermark patterns across all pages
"""
import sys
import pikepdf
import math

def analyze_transformation_matrix(matrix):
    """
    Analyze transformation matrix to understand its properties
    """
    if len(matrix) < 6:
        return None
    
    try:
        a, b, c, d, e, f = [float(x) for x in matrix[:6]]
        
        # Calculate properties
        scale_x = math.sqrt(a*a + b*b)
        scale_y = math.sqrt(c*c + d*d)
        
        if a != 0:
            angle = math.atan2(b, a) * 180 / math.pi
        else:
            angle = 90 if b > 0 else -90
        
        return {
            'matrix': [a, b, c, d, e, f],
            'scale_x': scale_x,
            'scale_y': scale_y,
            'angle': angle,
            'translation': [e, f]
        }
    except:
        return None

def comprehensive_scan(pdf_path):
    """
    Scan all pages to find watermark patterns
    """
    print(f"Comprehensive watermark scan: {pdf_path}")
    print("=" * 80)
    
    watermark_patterns = {}
    text_patterns = set()
    matrix_patterns = set()
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Sample pages across the document
            sample_pages = [0, 1, 2, 3, 4, 10, 20, 50, 100, 200, 300, 500, 700]
            sample_pages = [p for p in sample_pages if p < len(pdf.pages)]
            
            for page_idx in sample_pages:
                page = pdf.pages[page_idx]
                print(f"\n--- PAGE {page_idx + 1} ---")
                
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    
                    # Find all text blocks
                    text_blocks = []
                    i = 0
                    while i < len(operations):
                        operands, operator = operations[i]
                        if str(operator) == "BT":
                            # Collect text block
                            block_ops = []
                            i += 1
                            while i < len(operations):
                                operands, operator = operations[i]
                                block_ops.append((operands, operator))
                                if str(operator) == "ET":
                                    break
                                i += 1
                            text_blocks.append(block_ops)
                        i += 1
                    
                    print(f"Text blocks found: {len(text_blocks)}")
                    
                    # Analyze each text block
                    suspicious_blocks = []
                    for block_idx, block_ops in enumerate(text_blocks):
                        block_info = {
                            'matrices': [],
                            'texts': [],
                            'has_rotation': False,
                            'has_encoded_text': False,
                            'has_url_text': False
                        }
                        
                        for operands, operator in block_ops:
                            op_str = str(operator)
                            
                            if op_str == "Tm":  # Text matrix
                                matrix_info = analyze_transformation_matrix(operands)
                                if matrix_info:
                                    block_info['matrices'].append(matrix_info)
                                    
                                    # Check for significant rotation
                                    if abs(matrix_info['angle']) > 20:
                                        block_info['has_rotation'] = True
                                    
                                    # Store unique matrix patterns
                                    matrix_key = tuple(round(x, 3) for x in matrix_info['matrix'])
                                    matrix_patterns.add(matrix_key)
                            
                            elif op_str in ["Tj", "TJ", "'", '"']:
                                text_content = str(operands)
                                block_info['texts'].append(text_content)
                                
                                # Check for encoded patterns
                                if any(pattern in text_content.upper() for pattern in [
                                    "KWWSV", "JLWKX", "FKHQ", "OXHQW", "WKRQ"
                                ]):
                                    block_info['has_encoded_text'] = True
                                    text_patterns.add(text_content)
                                
                                # Check for URL patterns
                                if any(pattern in text_content.upper() for pattern in [
                                    "HTTPS", "GITHUB", "FLUENT", "PYTHON", ".COM"
                                ]):
                                    block_info['has_url_text'] = True
                                    text_patterns.add(text_content)
                        
                        # Determine if this block is suspicious
                        is_suspicious = (
                            block_info['has_rotation'] or 
                            block_info['has_encoded_text'] or 
                            block_info['has_url_text']
                        )
                        
                        if is_suspicious:
                            suspicious_blocks.append((block_idx, block_info))
                    
                    if suspicious_blocks:
                        print(f"*** SUSPICIOUS BLOCKS: {len(suspicious_blocks)} ***")
                        for block_idx, info in suspicious_blocks:
                            print(f"  Block {block_idx}:")
                            if info['has_rotation']:
                                print(f"    - Has rotation: {info['matrices'][0]['angle']:.1f}°")
                            if info['has_encoded_text']:
                                print(f"    - Has encoded text: {info['texts'][:2]}")
                            if info['has_url_text']:
                                print(f"    - Has URL text: {info['texts'][:2]}")
                        
                        # Store pattern for this page
                        watermark_patterns[page_idx + 1] = len(suspicious_blocks)
                    else:
                        print("No suspicious blocks found")
                        watermark_patterns[page_idx + 1] = 0
                
                except Exception as e:
                    print(f"Error analyzing page {page_idx + 1}: {e}")
            
            # Summary
            print(f"\n{'='*60}")
            print("WATERMARK PATTERN SUMMARY:")
            print(f"{'='*60}")
            
            print(f"Pages with watermarks: {sum(1 for count in watermark_patterns.values() if count > 0)}")
            print(f"Total pages scanned: {len(watermark_patterns)}")
            
            print(f"\nWatermarks per page:")
            for page_num, count in sorted(watermark_patterns.items()):
                if count > 0:
                    print(f"  Page {page_num}: {count} watermarks")
            
            print(f"\nUnique text patterns found: {len(text_patterns)}")
            for pattern in sorted(text_patterns):
                print(f"  {pattern}")
            
            print(f"\nUnique transformation matrices: {len(matrix_patterns)}")
            for matrix in sorted(matrix_patterns):
                print(f"  {matrix}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python comprehensive_watermark_scan.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    comprehensive_scan(pdf_file)
