#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Remove the actual text watermarks (rotated text objects with encoded content)
"""
import sys
import pikepdf

def remove_text_watermarks_correct(input_pdf, output_pdf):
    """
    Remove the rotated text watermarks that can be selected in PDF editors
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    watermarks_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Parse content stream operations
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    i = 0
                    while i < len(operations):
                        operands, operator = operations[i]
                        op_str = str(operator)
                        
                        # Look for text blocks that might be watermarks
                        if op_str == "BT":  # Begin text block
                            # Collect all operations in this text block
                            text_block_ops = [(operands, operator)]
                            i += 1
                            
                            is_watermark_block = False
                            
                            # Read until ET (End text)
                            while i < len(operations):
                                operands, operator = operations[i]
                                op_str = str(operator)
                                text_block_ops.append((operands, operator))
                                
                                # Check for watermark indicators
                                if op_str == "Tm":  # Text matrix
                                    # Check for the specific transformation matrix pattern
                                    if (len(operands) >= 6 and 
                                        abs(float(operands[0]) - 2.16507) < 0.001 and
                                        abs(float(operands[1]) - 1.25) < 0.001):
                                        is_watermark_block = True
                                
                                elif op_str in ["Tj", "TJ"]:  # Text showing
                                    # Check for encoded watermark text
                                    text_content = str(operands)
                                    if any(pattern in text_content for pattern in [
                                        "KWWSV", "JLWKX", "FKHQ", "OXHQW", "WKRQ"
                                    ]):
                                        is_watermark_block = True
                                
                                if op_str == "ET":  # End text block
                                    break
                                
                                i += 1
                            
                            # Decide whether to keep or remove this text block
                            if is_watermark_block:
                                watermarks_removed += 1
                                page_modified = True
                                if page_num < 10:  # Print for first 10 pages
                                    print(f"  Page {page_num + 1}: Removed watermark text block")
                                # Skip this entire text block (don't add to new_operations)
                            else:
                                # Keep this text block
                                new_operations.extend(text_block_ops)
                        
                        else:
                            # Non-text operations, keep them
                            new_operations.append((operands, operator))
                        
                        i += 1
                    
                    # Update page content if modified
                    if page_modified:
                        try:
                            new_content = pikepdf.unparse_content_stream(new_operations)
                            page["/Contents"] = pikepdf.Stream(pdf, new_content)
                            pages_processed += 1
                        except Exception as e:
                            print(f"  Page {page_num + 1}: Error updating content - {e}")
                    
                    # Progress indicator
                    if (page_num + 1) % 100 == 0:
                        print(f"  Processed {page_num + 1} pages...")
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Watermark text blocks removed: {watermarks_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_text_watermarks_correct.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_text_watermarks_correct(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Text watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
