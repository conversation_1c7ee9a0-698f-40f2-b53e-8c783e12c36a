#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Precise watermark remover - only remove watermark operations, keep content
"""
import sys
import pikepdf

def precise_remove_watermarks(input_pdf, output_pdf):
    """
    Precisely remove watermarks without destroying page content
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_modified = False
                    
                    # 1. Remove watermark XObjects from resources
                    if "/Resources" in page:
                        resources = page["/Resources"]
                        if "/XObject" in resources:
                            xobjects = dict(resources["/XObject"])
                            original_count = len(xobjects)
                            
                            # Identify watermark XObjects
                            watermark_xobjects = []
                            
                            for name, xobj in list(xobjects.items()):
                                try:
                                    subtype = xobj.get("/Subtype", "Unknown")
                                    
                                    # Check if it's a watermark
                                    is_watermark = False
                                    
                                    # Form XObjects that might contain watermarks
                                    if subtype == pikepdf.Name("/Form"):
                                        name_str = str(name)
                                        if any(pattern in name_str for pattern in ["/Fm"]):
                                            is_watermark = True
                                    
                                    # Large Image XObjects (likely watermarks)
                                    elif subtype == pikepdf.Name("/Image"):
                                        if hasattr(xobj, 'read_bytes'):
                                            try:
                                                size = len(xobj.read_bytes())
                                                # Images larger than 4MB are likely watermarks
                                                if size > 4000000:
                                                    is_watermark = True
                                            except:
                                                pass
                                        
                                        # Also check by name pattern
                                        name_str = str(name)
                                        if any(pattern in name_str for pattern in ["/Im", "/X4", "/Image1"]):
                                            is_watermark = True
                                    
                                    if is_watermark:
                                        watermark_xobjects.append(name_str)
                                        del xobjects[name_str]
                                        
                                except Exception as e:
                                    pass
                            
                            # Update resources if watermarks were removed
                            if len(xobjects) < original_count:
                                if xobjects:
                                    resources["/XObject"] = pikepdf.Dictionary(xobjects)
                                else:
                                    del resources["/XObject"]
                                page["/Resources"] = resources
                                page_modified = True
                                
                                if page_num < 10:  # Print for first 10 pages
                                    print(f"  Page {page_num + 1}: Removed XObjects: {watermark_xobjects}")
                    
                    # 2. Remove Do operations that call watermark XObjects
                    if "/Contents" in page:
                        contents = page["/Contents"]
                        
                        if isinstance(contents, pikepdf.Array):
                            # Multiple content streams
                            new_contents = []
                            
                            for i, stream in enumerate(contents):
                                if hasattr(stream, 'read_bytes'):
                                    try:
                                        # Parse operations in this stream
                                        operations = list(pikepdf.parse_content_stream(stream, ""))
                                        new_operations = []
                                        stream_modified = False
                                        
                                        for operands, operator in operations:
                                            should_remove = False
                                            
                                            # Remove Do operations for watermark XObjects
                                            if str(operator) == "Do" and operands:
                                                xobj_name = str(operands[0])
                                                
                                                # Check if this is a watermark XObject
                                                if any(pattern in xobj_name for pattern in [
                                                    "/Fm0", "/Fm1", "/X4", "/Image1", "/Im"
                                                ]):
                                                    should_remove = True
                                                    operations_removed += 1
                                                    stream_modified = True
                                                    
                                                    if page_num < 10:
                                                        print(f"    Stream {i}: Removed Do {xobj_name}")
                                            
                                            if not should_remove:
                                                new_operations.append((operands, operator))
                                        
                                        # Update stream if modified
                                        if stream_modified:
                                            new_content = pikepdf.unparse_content_stream(new_operations)
                                            new_stream = pikepdf.Stream(pdf, new_content)
                                            new_contents.append(new_stream)
                                            page_modified = True
                                        else:
                                            new_contents.append(stream)
                                            
                                    except Exception as e:
                                        # Keep stream if can't process
                                        new_contents.append(stream)
                                else:
                                    new_contents.append(stream)
                            
                            # Update page contents
                            if page_modified:
                                page["/Contents"] = pikepdf.Array(new_contents)
                        
                        else:
                            # Single content stream
                            try:
                                operations = list(pikepdf.parse_content_stream(contents, ""))
                                new_operations = []
                                
                                for operands, operator in operations:
                                    should_remove = False
                                    
                                    # Remove Do operations for watermark XObjects
                                    if str(operator) == "Do" and operands:
                                        xobj_name = str(operands[0])
                                        
                                        if any(pattern in xobj_name for pattern in [
                                            "/Fm0", "/Fm1", "/X4", "/Image1", "/Im"
                                        ]):
                                            should_remove = True
                                            operations_removed += 1
                                            page_modified = True
                                            
                                            if page_num < 10:
                                                print(f"    Removed Do {xobj_name}")
                                    
                                    if not should_remove:
                                        new_operations.append((operands, operator))
                                
                                # Update content if modified
                                if page_modified:
                                    new_content = pikepdf.unparse_content_stream(new_operations)
                                    page["/Contents"] = pikepdf.Stream(pdf, new_content)
                                    
                            except Exception as e:
                                print(f"    Error processing single stream: {e}")
                    
                    if page_modified:
                        pages_processed += 1
                    
                    # Progress indicator
                    if (page_num + 1) % 100 == 0:
                        print(f"  Processed {page_num + 1} pages...")
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Do operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python precise_watermark_remover.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not precise_remove_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Precise watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
