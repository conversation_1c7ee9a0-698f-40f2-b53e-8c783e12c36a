#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Detailed scan of specific pages to understand watermark distribution
"""
import sys
import pikepdf
import math

def scan_specific_pages(pdf_path):
    """
    Scan specific pages to understand watermark patterns
    """
    print(f"Detailed page scan: {pdf_path}")
    print("=" * 60)
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check many more pages
            pages_to_check = list(range(1, 21)) + [30, 40, 50, 60, 70, 80, 90, 100, 
                                                   150, 200, 250, 300, 350, 400, 450, 500,
                                                   550, 600, 650, 700, 750, 800]
            pages_to_check = [p-1 for p in pages_to_check if p <= len(pdf.pages)]  # Convert to 0-based
            
            watermark_pages = []
            
            for page_idx in pages_to_check:
                page = pdf.pages[page_idx]
                
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    
                    # Count rotated text blocks
                    rotated_blocks = 0
                    watermark_blocks = 0
                    
                    i = 0
                    while i < len(operations):
                        operands, operator = operations[i]
                        if str(operator) == "BT":
                            # Analyze this text block
                            has_rotation = False
                            has_watermark_matrix = False
                            
                            i += 1
                            while i < len(operations):
                                operands, operator = operations[i]
                                op_str = str(operator)
                                
                                if op_str == "Tm":
                                    if len(operands) >= 6:
                                        try:
                                            a, b, c, d = [float(x) for x in operands[:4]]
                                            
                                            # Check for exact watermark matrix
                                            if (abs(a - 2.165) < 0.01 and abs(b - 1.25) < 0.01 and 
                                                abs(c + 1.25) < 0.01 and abs(d - 2.165) < 0.01):
                                                has_watermark_matrix = True
                                            
                                            # Check for any significant rotation
                                            if a != 0:
                                                angle = math.atan2(b, a) * 180 / math.pi
                                                if abs(angle) > 20:
                                                    has_rotation = True
                                        except:
                                            pass
                                
                                if op_str == "ET":
                                    break
                                i += 1
                            
                            if has_watermark_matrix:
                                watermark_blocks += 1
                            elif has_rotation:
                                rotated_blocks += 1
                        i += 1
                    
                    if watermark_blocks > 0 or rotated_blocks > 0:
                        result = f"Page {page_idx + 1}: "
                        if watermark_blocks > 0:
                            result += f"{watermark_blocks} watermark blocks"
                        if rotated_blocks > 0:
                            if watermark_blocks > 0:
                                result += f", {rotated_blocks} other rotated blocks"
                            else:
                                result += f"{rotated_blocks} rotated blocks"
                        print(result)
                        
                        if watermark_blocks > 0:
                            watermark_pages.append(page_idx + 1)
                
                except Exception as e:
                    print(f"Page {page_idx + 1}: Error - {e}")
            
            print(f"\nSummary:")
            print(f"Pages scanned: {len(pages_to_check)}")
            print(f"Pages with watermarks: {len(watermark_pages)}")
            print(f"Watermark pages: {watermark_pages}")
            
            # If we found very few watermarks, the issue might be elsewhere
            if len(watermark_pages) < 10:
                print(f"\n⚠️ Only found {len(watermark_pages)} pages with watermarks.")
                print("This suggests either:")
                print("1. Most pages don't have watermarks")
                print("2. Watermarks use different patterns we haven't detected")
                print("3. Watermarks are stored differently (annotations, form fields, etc.)")
                
                # Let's check if there are annotations or form fields
                print(f"\nChecking for other watermark storage methods...")
                
                annotation_pages = 0
                form_field_pages = 0
                
                for page_idx in pages_to_check[:10]:  # Check first 10 pages
                    page = pdf.pages[page_idx]
                    
                    if "/Annots" in page:
                        annotation_pages += 1
                    
                if "/AcroForm" in pdf.Root:
                    print("Document has form fields")
                
                print(f"Pages with annotations: {annotation_pages}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python detailed_page_scan.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    scan_specific_pages(pdf_file)
