#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final verification - check if watermarks can still be found
"""
import sys
import pikepdf

def final_verification(pdf_path):
    """
    Final verification of watermark removal
    """
    print(f"Final verification of: {pdf_path}")
    print("=" * 60)
    
    watermark_found = False
    total_pages_checked = 0
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check first 20 pages and some random pages
            pages_to_check = list(range(20)) + [50, 100, 200, 300, 500, 700]
            pages_to_check = [p for p in pages_to_check if p < len(pdf.pages)]
            
            for page_idx in pages_to_check:
                page = pdf.pages[page_idx]
                total_pages_checked += 1
                
                # Check for watermark XObjects
                watermark_xobjects = []
                if "/Resources" in page:
                    resources = page["/Resources"]
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        for name in xobjects.keys():
                            name_str = str(name)
                            if any(pattern in name_str for pattern in ["/Fm", "/Im", "/X4", "/Image1"]):
                                watermark_xobjects.append(name_str)
                
                # Check for Do operations calling watermarks
                watermark_do_ops = []
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    for i, (operands, operator) in enumerate(operations):
                        if str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            if any(pattern in xobj_name for pattern in ["/Fm", "/Im", "/X4", "/Image1"]):
                                watermark_do_ops.append(xobj_name)
                except:
                    pass
                
                # Check for text watermarks
                text_watermarks = []
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    for i, (operands, operator) in enumerate(operations):
                        if str(operator) in ["TJ", "Tj"]:
                            operands_str = str(operands)
                            if any(pattern in operands_str for pattern in [
                                "KWWSV", "JLWKX", "github", "chenyz", "FluentPython"
                            ]):
                                text_watermarks.append(operands_str[:50])
                except:
                    pass
                
                # Report findings
                if watermark_xobjects or watermark_do_ops or text_watermarks:
                    watermark_found = True
                    print(f"Page {page_idx + 1}: ❌ Watermarks found")
                    if watermark_xobjects:
                        print(f"  XObjects: {watermark_xobjects}")
                    if watermark_do_ops:
                        print(f"  Do operations: {watermark_do_ops}")
                    if text_watermarks:
                        print(f"  Text watermarks: {text_watermarks}")
                elif page_idx < 10:  # Only print for first 10 pages to avoid spam
                    print(f"Page {page_idx + 1}: ✅ Clean")
            
            print(f"\nVerification Summary:")
            print(f"  Pages checked: {total_pages_checked}")
            
            if watermark_found:
                print(f"  Result: ❌ Watermarks still present")
            else:
                print(f"  Result: ✅ No watermarks detected")
                print(f"  🎉 PDF appears to be clean!")
                
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return not watermark_found

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python final_verification.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    success = final_verification(pdf_file)
    
    if success:
        print("\n🎉 SUCCESS: PDF is clean!")
    else:
        print("\n⚠️ WARNING: Some watermarks may still be present")
