#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verify that watermarks have been successfully removed
"""
import sys
import pikepdf

def verify_watermark_removal(pdf_path):
    """
    Check if watermarks have been removed from the PDF
    """
    print(f"Verifying: {pdf_path}")
    print("=" * 50)
    
    watermark_found = False
    pages_with_watermarks = 0
    
    try:
        with pikepdf.Pdf.open(pdf_path) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            # Check first 10 pages for watermark patterns
            for page_num in range(min(10, len(pdf.pages))):
                page = pdf.pages[page_num]
                page_has_watermark = False
                
                try:
                    # Check for watermark XObjects in resources
                    resources = page.get("/Resources", {})
                    if "/XObject" in resources:
                        xobjects = resources["/XObject"]
                        watermark_xobjs = []
                        
                        for name, xobj in xobjects.items():
                            name_str = str(name)
                            # Check for common watermark patterns
                            if any(pattern in name_str for pattern in ["/Fm", "/X4", "/Image1"]):
                                watermark_xobjs.append(name_str)
                        
                        if watermark_xobjs:
                            print(f"  Page {page_num + 1}: Found watermark XObjects: {watermark_xobjs}")
                            page_has_watermark = True
                    
                    # Check content stream for Do operations calling watermarks
                    operations = pikepdf.parse_content_stream(page, "")
                    watermark_calls = []
                    
                    for operands, operator in operations:
                        if str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            if any(pattern in xobj_name for pattern in ["/Fm", "/X4", "/Image1"]):
                                watermark_calls.append(xobj_name)
                    
                    if watermark_calls:
                        print(f"  Page {page_num + 1}: Found watermark Do calls: {watermark_calls}")
                        page_has_watermark = True
                    
                    if not page_has_watermark:
                        print(f"  Page {page_num + 1}: ✓ No watermarks detected")
                    else:
                        pages_with_watermarks += 1
                        watermark_found = True
                        
                except Exception as e:
                    print(f"  Page {page_num + 1}: Error checking - {e}")
            
            print(f"\nSummary:")
            print(f"  Pages checked: {min(10, len(pdf.pages))}")
            print(f"  Pages with watermarks: {pages_with_watermarks}")
            
            if watermark_found:
                print("  ❌ Watermarks still present")
            else:
                print("  ✅ No watermarks detected in checked pages")
                
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return not watermark_found

def compare_pdfs(original_pdf, cleaned_pdf):
    """
    Compare original and cleaned PDFs
    """
    print(f"\nComparing PDFs:")
    print(f"Original: {original_pdf}")
    print(f"Cleaned:  {cleaned_pdf}")
    print("=" * 50)
    
    try:
        with pikepdf.Pdf.open(original_pdf) as orig, pikepdf.Pdf.open(cleaned_pdf) as clean:
            print(f"Original pages: {len(orig.pages)}")
            print(f"Cleaned pages:  {len(clean.pages)}")
            
            if len(orig.pages) != len(clean.pages):
                print("❌ Page count mismatch!")
                return False
            
            # Check a few pages for differences
            for page_num in [0, 1, 2]:  # Check first 3 pages
                if page_num >= len(orig.pages):
                    break
                    
                orig_page = orig.pages[page_num]
                clean_page = clean.pages[page_num]
                
                # Count operations in content streams
                try:
                    orig_ops = list(pikepdf.parse_content_stream(orig_page, ""))
                    clean_ops = list(pikepdf.parse_content_stream(clean_page, ""))
                    
                    print(f"  Page {page_num + 1}:")
                    print(f"    Original operations: {len(orig_ops)}")
                    print(f"    Cleaned operations:  {len(clean_ops)}")
                    print(f"    Difference: {len(orig_ops) - len(clean_ops)} operations removed")
                    
                except Exception as e:
                    print(f"    Error comparing page {page_num + 1}: {e}")
            
            print("✅ Comparison completed")
            
    except Exception as e:
        print(f"Error comparing PDFs: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python verify_removal.py <cleaned_pdf> [original_pdf]")
        sys.exit(1)
    
    cleaned_pdf = sys.argv[1]
    
    # Verify the cleaned PDF
    success = verify_watermark_removal(cleaned_pdf)
    
    # Compare with original if provided
    if len(sys.argv) >= 3:
        original_pdf = sys.argv[2]
        compare_pdfs(original_pdf, cleaned_pdf)
    
    if success:
        print("\n🎉 Verification successful! Watermarks appear to be removed.")
    else:
        print("\n⚠️  Verification failed. Some watermarks may still be present.")
