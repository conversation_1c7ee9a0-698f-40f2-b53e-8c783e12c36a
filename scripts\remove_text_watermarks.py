#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Remove text-based watermarks by pattern matching
"""
import sys
import pikepdf

def remove_text_watermarks(input_pdf, output_pdf):
    """
    Remove text watermarks by identifying and removing suspicious text operations
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    # Patterns that indicate watermark text (from our analysis)
    watermark_patterns = [
        "KWWSV",      # HTTPS encoded
        "JLWKX",      # GITHUB encoded  
        "FKHQ",       # CHEN encoded
        "IOXHQW",     # FLUENT encoded
        "3\\WKRQ",    # PYTHON encoded
        "JLWKXEFRPFKHQ",  # github.com/chen encoded
        "github.com",
        "chenyz1984",
        "FluentPython",
        "https://",
    ]
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        should_remove = False
                        
                        # Check all text operations
                        if str(operator) in ["TJ", "Tj", "'", '"']:
                            try:
                                # Convert operands to string for pattern matching
                                operands_str = str(operands)
                                
                                # Check for watermark patterns
                                for pattern in watermark_patterns:
                                    if pattern in operands_str:
                                        should_remove = True
                                        operations_removed += 1
                                        page_modified = True
                                        
                                        if page_num < 5:  # Print for first few pages
                                            print(f"  Page {page_num + 1}: Removing text operation with pattern '{pattern}'")
                                            print(f"    Content: {operands_str[:100]}...")
                                        break
                                
                                # Additional check for suspicious encoded patterns
                                if not should_remove:
                                    # Look for sequences of uppercase letters that might be encoded
                                    import re
                                    uppercase_sequences = re.findall(r'[A-Z]{4,}', operands_str)
                                    for seq in uppercase_sequences:
                                        if len(seq) >= 5:  # Suspicious long uppercase sequences
                                            # Quick Caesar cipher check
                                            for shift in [3]:  # Most common shift we found
                                                decoded = ""
                                                for char in seq:
                                                    if char.isalpha():
                                                        decoded += chr((ord(char) - ord('A') - shift) % 26 + ord('A'))
                                                
                                                if any(word in decoded for word in ["HTTPS", "GITHUB", "CHEN", "FLUENT"]):
                                                    should_remove = True
                                                    operations_removed += 1
                                                    page_modified = True
                                                    
                                                    if page_num < 5:
                                                        print(f"  Page {page_num + 1}: Removing encoded text: {seq} -> {decoded}")
                                                    break
                                            
                                            if should_remove:
                                                break
                                        
                            except Exception as e:
                                pass
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Text operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python remove_text_watermarks.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_text_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Text watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
