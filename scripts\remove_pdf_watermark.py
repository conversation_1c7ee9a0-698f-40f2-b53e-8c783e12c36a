#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF watermark analyzer & remover (targeting searchable/selectable text watermarks)

Usage:
  # 1) Analyze what the watermark is
  python scripts/remove_pdf_watermark.py --analyze input.pdf \
      --pattern "https://github.com/chenyz1984/FluentPython2ndCN"

  # 2) Remove the watermark(s) and save to a new file
  python scripts/remove_pdf_watermark.py --remove input.pdf cleaned.pdf \
      --pattern "https://github.com/chenyz1984/FluentPython2ndCN"

Notes:
- This script requires pikepdf (built on qpdf). Install with: pip install -U pikepdf
- It avoids "covering" content; it removes drawing instructions that render the watermark.
- Removal strategy (in order):
  1) Detect Form XObjects whose stream contains the target pattern; remove their Do calls.
  2) Remove annotation watermarks (Subtype /Watermark or /FreeText with matching text).
  3) As a fallback, remove text-draw operators (Tj/TJ/'/") containing the pattern.

Caveats:
- If the watermark text is encoded via font glyphs without the literal ASCII string in streams,
  step (1) may fail to detect. Step(3) tries to catch Tj/TJ text, but decoding may be font-dependent.
- Always keep a backup of your PDF.
"""
from __future__ import annotations
import argparse
import sys
from typing import Dict, List, Set, Tuple

try:
    import pikepdf
    from pikepdf import Pdf, Name, ContentStream
except Exception as e:
    print("ERROR: This script needs 'pikepdf'. Install: pip install -U pikepdf", file=sys.stderr)
    raise


TextOps = {b"Tj", b"TJ", b"'", b'"'}


def _to_bytes(obj) -> bytes:
    """Best-effort get raw bytes for a pikepdf stream or string-like object."""
    if obj is None:
        return b""
    # Stream objects
    if isinstance(obj, pikepdf.Stream):
        # try the common APIs for stream bytes
        for attr in ("read_bytes", "read_raw_bytes"):
            if hasattr(obj, attr):
                try:
                    return getattr(obj, attr)()
                except Exception:
                    pass
        try:
            return bytes(obj)
        except Exception:
            return b""
    # String-like
    try:
        # pikepdf.String behaves like Python str when cast
        s = str(obj)
        return s.encode("utf-8", errors="ignore")
    except Exception:
        return b""


def _norm_bytes(b: bytes) -> bytes:
    return b.lower().strip()


def _extract_text_from_operands(operands, operator: bytes) -> str:
    """Return concatenated text for Tj/TJ/'/" operators, best-effort decoding to unicode."""
    try:
        if operator == b"Tj" or operator == b"'":
            if not operands:
                return ""
            return str(operands[0])
        if operator == b"TJ":
            # Array of strings and numbers; join strings only
            arr = operands[0]
            parts = []
            for item in arr:
                # Only keep strings; ignore kerning numbers
                try:
                    parts.append(str(item))
                except Exception:
                    pass
            return "".join(parts)
        if operator == b'"':
            # (aw ac) string -> operands[2]
            if len(operands) >= 3:
                return str(operands[2])
            return ""
    except Exception:
        return ""
    return ""


def analyze_pdf(input_pdf: str, pattern: str) -> Dict:
    """Analyze the PDF and report where the watermark pattern comes from.

    Returns a dict with details: pages, xobject_hits, xobject_usage_count, text_hits, annot_hits.
    """
    pat_b = _norm_bytes(pattern.encode("utf-8", errors="ignore"))

    report = {
        "pages": 0,
        "xobject_hits": {},  # page_index -> set of Name objects that are watermark XObjects in Resources
        "xobject_usage_count": {},  # page_index -> count of Do operator calls to those XObjects
        "text_hits": {},  # page_index -> count of text ops containing pattern
        "annot_hits": {},  # page_index -> list of annotation dict summaries
    }

    with Pdf.open(input_pdf) as pdf:
        report["pages"] = len(pdf.pages)
        for pi, page in enumerate(pdf.pages):
            page_wm_xobjs: Set[Name] = set()
            usage_count = 0
            text_hit_count = 0
            annot_summaries: List[str] = []

            # 1) Scan XObjects in Resources for the literal pattern
            try:
                resources = page.get("/Resources", None)
                if resources and "/XObject" in resources:
                    xobjs = resources["/XObject"]
                    for name, xobj in list(xobjs.items()):
                        try:
                            # Only consider Form XObjects (Subtype /Form) – typical for repeated watermarks
                            if isinstance(xobj, pikepdf.Stream):
                                subtype = xobj.get("/Subtype", None)
                                if subtype == Name("/Form"):
                                    stream_bytes = _to_bytes(xobj)
                                    if pat_b in _norm_bytes(stream_bytes):
                                        page_wm_xobjs.add(name)
                        except Exception:
                            pass
            except Exception:
                pass

            # 2) Count usages of those XObjects in the page content stream (Do operator)
            try:
                cs = ContentStream(page.contents, pdf)
                for operands, operator in cs.operations:
                    if operator == b"Do" and operands:
                        name = operands[0]
                        if name in page_wm_xobjs:
                            usage_count += 1
                    # 3) Fallback: literal text in text operators
                    if operator in TextOps:
                        text_s = _extract_text_from_operands(operands, operator)
                        if pattern.lower() in text_s.lower():
                            text_hit_count += 1
            except Exception:
                pass

            # 4) Check annotations that could represent a watermark
            try:
                annots = page.get("/Annots", None)
                if annots:
                    for a in annots:
                        try:
                            sub = a.get("/Subtype", None)
                            # Some producers may use /Watermark or /FreeText
                            if sub in {Name("/Watermark"), Name("/FreeText")}:
                                contents = str(a.get("/Contents", ""))
                                if contents and pattern.lower() in contents.lower():
                                    annot_summaries.append(f"{sub} : {contents[:80]}")
                        except Exception:
                            pass
            except Exception:
                pass

            if page_wm_xobjs:
                report["xobject_hits"][pi] = {str(n): True for n in page_wm_xobjs}
            if usage_count:
                report["xobject_usage_count"][pi] = usage_count
            if text_hit_count:
                report["text_hits"][pi] = text_hit_count
            if annot_summaries:
                report["annot_hits"][pi] = annot_summaries

    return report


def remove_watermarks(input_pdf: str, output_pdf: str, pattern: str, dry_run: bool = False) -> Dict:
    """Remove watermarks matching pattern. Returns a summary dict of actions taken.

    Strategy:
      - Identify Form XObjects whose stream contains the pattern; drop their Do calls.
      - Remove annotation watermarks that contain the pattern in /Contents.
      - Fallback: Remove text-draw operators that contain the pattern directly.
    """
    pat_b = _norm_bytes(pattern.encode("utf-8", errors="ignore"))
    summary = {
        "pages": 0,
        "do_calls_removed": 0,
        "text_ops_removed": 0,
        "annots_removed": 0,
        "pages_touched": 0,
    }

    with Pdf.open(input_pdf) as pdf:
        summary["pages"] = len(pdf.pages)
        pages_touched = 0
        total_do_removed = 0
        total_text_removed = 0
        total_annots_removed = 0

        for pi, page in enumerate(pdf.pages):
            page_touched = False

            # 1) Build the set of watermark XObjects on this page
            wm_xobjs: Set[Name] = set()
            try:
                resources = page.get("/Resources", None)
                if resources and "/XObject" in resources:
                    xobjs = resources["/XObject"]
                    for name, xobj in list(xobjs.items()):
                        try:
                            if isinstance(xobj, pikepdf.Stream) and xobj.get("/Subtype", None) == Name("/Form"):
                                if pat_b in _norm_bytes(_to_bytes(xobj)):
                                    wm_xobjs.add(name)
                        except Exception:
                            pass
            except Exception:
                pass

            # 2) Rewrite content stream removing Do calls of those XObjects and text ops containing pattern
            do_removed = 0
            text_removed = 0
            try:
                cs = ContentStream(page.contents, pdf)
                new_ops: List[Tuple[List, bytes]] = []
                for operands, operator in cs.operations:
                    # Remove Do calls to watermark XObjects
                    if operator == b"Do" and operands and operands[0] in wm_xobjs:
                        do_removed += 1
                        page_touched = True
                        continue
                    # Fallback: remove text draw ops that contain the pattern directly
                    if operator in TextOps:
                        text_s = _extract_text_from_operands(operands, operator)
                        if pattern.lower() in text_s.lower():
                            text_removed += 1
                            page_touched = True
                            continue
                    new_ops.append((operands, operator))
                if page_touched and not dry_run:
                    cs.operations = new_ops
                    page.contents = cs
            except Exception:
                pass

            # 3) Remove matching annotations
            try:
                annots = page.get("/Annots", None)
                if annots:
                    to_keep = []
                    removed_here = 0
                    for a in annots:
                        try:
                            sub = a.get("/Subtype", None)
                            if sub in {Name("/Watermark"), Name("/FreeText")}:
                                contents = str(a.get("/Contents", ""))
                                if contents and pattern.lower() in contents.lower():
                                    removed_here += 1
                                    page_touched = True
                                    continue  # skip
                        except Exception:
                            pass
                        to_keep.append(a)
                    if page_touched and not dry_run:
                        if to_keep:
                            page["/Annots"] = pikepdf.Array(to_keep)
                        else:
                            if "/Annots" in page:
                                del page["/Annots"]
                    total_annots_removed += removed_here
            except Exception:
                pass

            total_do_removed += do_removed
            total_text_removed += text_removed
            if page_touched:
                pages_touched += 1

        if not dry_run:
            # Preserve original version where possible, compress streams
            pdf.save(output_pdf, linearize=False)

    summary.update({
        "do_calls_removed": total_do_removed,
        "text_ops_removed": total_text_removed,
        "annots_removed": total_annots_removed,
        "pages_touched": pages_touched,
    })
    return summary


def main():
    parser = argparse.ArgumentParser(description="Analyze & remove text watermarks from PDF (pikepdf)")
    parser.add_argument("input", help="Input PDF path")
    parser.add_argument("output", nargs="?", help="Output PDF path (when --remove)")
    parser.add_argument("--pattern", required=True, help="Target watermark text to detect/remove")
    parser.add_argument("--analyze", action="store_true", help="Analyze only, don't modify")
    parser.add_argument("--remove", action="store_true", help="Remove matching watermarks and save to output")
    parser.add_argument("--dry-run", action="store_true", help="Simulate removal without writing output file")

    args = parser.parse_args()

    if args.analyze:
        rpt = analyze_pdf(args.input, args.pattern)
        print("=== Analysis Report ===")
        print(f"Pages: {rpt['pages']}")
        print(f"Pages with watermark Form XObjects: {len(rpt['xobject_hits'])}")
        print(f"Pages with XObject Do uses: {len(rpt['xobject_usage_count'])}")
        print(f"Pages with direct text hits: {len(rpt['text_hits'])}")
        print(f"Pages with annotation hits: {len(rpt['annot_hits'])}")
        # brief samples
        for k in sorted(rpt["xobject_hits"].keys()):
            names = ", ".join(sorted(rpt["xobject_hits"][k].keys()))
            uses = rpt["xobject_usage_count"].get(k, 0)
            print(f"  - Page {k+1}: XObjects {{ {names} }}, Do uses: {uses}")
        for k in sorted(rpt["text_hits" ].keys()):
            print(f"  - Page {k+1}: direct text ops containing pattern: {rpt['text_hits'][k]}")
        for k in sorted(rpt["annot_hits"].keys()):
            items = rpt["annot_hits"][k]
            print(f"  - Page {k+1}: annotation hits: {len(items)}")
        return

    if args.remove:
        if not args.output and not args.dry_run:
            print("ERROR: Output PDF path required for --remove (unless --dry-run)", file=sys.stderr)
            sys.exit(2)
        summary = remove_watermarks(args.input, args.output or "", args.pattern, dry_run=args.dry_run)
        print("=== Removal Summary ===")
        for k, v in summary.items():
            print(f"{k}: {v}")
        if not args.dry_run and args.output:
            print(f"Saved: {args.output}")
        return

    print("Nothing to do. Use --analyze or --remove.")


if __name__ == "__main__":
    main()

