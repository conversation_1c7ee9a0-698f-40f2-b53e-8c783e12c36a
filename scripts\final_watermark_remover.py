#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final watermark remover - handles multiple content streams correctly
"""
import sys
import pikepdf
import re

def final_remove_watermarks(input_pdf, output_pdf):
    """
    Remove watermarks by correctly handling multiple content streams
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    total_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    print(f"\nPage {page_num + 1}:")
                    page_modified = False
                    
                    # Handle /Contents
                    if "/Contents" in page:
                        contents = page["/Contents"]
                        
                        if isinstance(contents, pikepdf.Array):
                            print(f"  Multiple content streams: {len(contents)}")
                            new_contents = []
                            
                            for i, stream in enumerate(contents):
                                if hasattr(stream, 'read_bytes'):
                                    try:
                                        raw_content = stream.read_bytes()
                                        content_text = raw_content.decode('utf-8', errors='ignore')
                                        
                                        print(f"    Stream {i}: {len(content_text)} characters")
                                        
                                        # Check for watermark patterns
                                        watermark_patterns = [
                                            "KWWSV˛JLWKXEFRPFKHQ\\]˝˙)OXHQW3\\WKRQQG&1",
                                            "/Fm0 Do",
                                            "/Fm1 Do",
                                        ]
                                        
                                        has_watermark = any(pattern in content_text for pattern in watermark_patterns)
                                        
                                        if has_watermark:
                                            print(f"      *** WATERMARK FOUND - REMOVING STREAM {i} ***")
                                            page_modified = True
                                            total_removed += 1
                                            # Skip this stream (don't add to new_contents)
                                        else:
                                            print(f"      Stream {i}: Clean, keeping")
                                            new_contents.append(stream)
                                            
                                    except Exception as e:
                                        print(f"      Error reading stream {i}: {e}")
                                        new_contents.append(stream)  # Keep if can't read
                                else:
                                    print(f"      Stream {i}: Not readable, keeping")
                                    new_contents.append(stream)
                            
                            # Update page contents
                            if page_modified:
                                if new_contents:
                                    page["/Contents"] = pikepdf.Array(new_contents)
                                    print(f"    ✅ Updated contents: {len(contents)} -> {len(new_contents)} streams")
                                else:
                                    # If no content streams left, create an empty one
                                    empty_stream = pikepdf.Stream(pdf, b"")
                                    page["/Contents"] = pikepdf.Array([empty_stream])
                                    print(f"    ✅ All streams removed, created empty content")
                                
                                pages_processed += 1
                        
                        else:
                            # Single content stream
                            print(f"  Single content stream")
                            if hasattr(contents, 'read_bytes'):
                                try:
                                    raw_content = contents.read_bytes()
                                    content_text = raw_content.decode('utf-8', errors='ignore')
                                    
                                    # Check for watermark patterns
                                    watermark_patterns = [
                                        "KWWSV˛JLWKXEFRPFKHQ\\]˝˙)OXHQW3\\WKRQQG&1",
                                        "/Fm0 Do",
                                        "/Fm1 Do",
                                    ]
                                    
                                    has_watermark = any(pattern in content_text for pattern in watermark_patterns)
                                    
                                    if has_watermark:
                                        print(f"    *** WATERMARK FOUND - CREATING CLEAN CONTENT ***")
                                        
                                        # Remove watermark patterns
                                        new_content = content_text
                                        
                                        # Remove TJ operations with watermark text
                                        new_content = re.sub(r'\[\s*\([^)]*KWWSV[^)]*\)\s*\]\s*TJ', '', new_content)
                                        
                                        # Remove Do operations for watermark XObjects
                                        new_content = re.sub(r'/Fm[0-9]+\s+Do', '', new_content)
                                        new_content = re.sub(r'/X4\s+Do', '', new_content)
                                        new_content = re.sub(r'/Image1\s+Do', '', new_content)
                                        
                                        if len(new_content) < len(content_text):
                                            print(f"    Removed {len(content_text) - len(new_content)} characters")
                                            
                                            # Create new content stream
                                            new_content_bytes = new_content.encode('utf-8')
                                            page["/Contents"] = pikepdf.Stream(pdf, new_content_bytes)
                                            pages_processed += 1
                                            total_removed += 1
                                            page_modified = True
                                            print(f"    ✅ Content stream updated")
                                        else:
                                            print(f"    No changes made to content")
                                    else:
                                        print(f"    No watermarks found in content")
                                        
                                except Exception as e:
                                    print(f"    Error processing single stream: {e}")
                    
                    # Also clean up resources
                    if page_modified and "/Resources" in page:
                        resources = page["/Resources"]
                        if "/XObject" in resources:
                            xobjects = dict(resources["/XObject"])
                            
                            # Remove watermark XObjects
                            watermark_xobjs = ["/Fm0", "/Fm1", "/X4", "/Image1"]
                            removed_xobjs = []
                            
                            for xobj_name in watermark_xobjs:
                                if xobj_name in xobjects:
                                    del xobjects[xobj_name]
                                    removed_xobjs.append(xobj_name)
                            
                            if removed_xobjs:
                                if xobjects:
                                    resources["/XObject"] = pikepdf.Dictionary(xobjects)
                                else:
                                    del resources["/XObject"]
                                page["/Resources"] = resources
                                print(f"    ✅ Removed XObjects: {removed_xobjs}")
                    
                    # Process all pages
                    # if page_num >= 4:
                    #     break
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Total items removed: {total_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python final_watermark_remover.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not final_remove_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Final watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
