#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate watermark remover - target 30° rotated text blocks
Based on discovery: watermarks are text blocks with 30° rotation
Matrix pattern: (2.165, 1.25, -1.25, 2.165, 0.0, 0.0)
"""
import sys
import pikepdf
import math

def is_watermark_matrix(matrix):
    """
    Check if transformation matrix is the watermark pattern
    """
    if len(matrix) < 6:
        return False
    
    try:
        a, b, c, d, e, f = [float(x) for x in matrix[:6]]
        
        # Exact watermark matrix: (2.165, 1.25, -1.25, 2.165, 0.0, 0.0)
        if (abs(a - 2.165) < 0.01 and abs(b - 1.25) < 0.01 and 
            abs(c + 1.25) < 0.01 and abs(d - 2.165) < 0.01):
            return True
        
        # Also check for 30° rotation pattern
        if a != 0:
            angle = math.atan2(b, a) * 180 / math.pi
            scale = math.sqrt(a*a + b*b)
            
            # 30° rotation with scale around 2.5
            if abs(angle - 30) < 5 and scale > 2.0:
                return True
                
    except:
        pass
    
    return False

def remove_watermarks_ultimate(input_pdf, output_pdf):
    """
    Ultimate watermark removal targeting 30° rotated text blocks
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    total_watermarks_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_watermarks_removed = 0
                    
                    # Handle content streams
                    if "/Contents" in page:
                        contents = page["/Contents"]
                        
                        if isinstance(contents, pikepdf.Array):
                            # Multiple content streams
                            new_contents = []
                            
                            for stream_idx, stream in enumerate(contents):
                                if hasattr(stream, 'read_bytes'):
                                    try:
                                        operations = list(pikepdf.parse_content_stream(stream, ""))
                                        new_operations, removed = process_operations_ultimate(operations, page_num)
                                        
                                        if removed > 0:
                                            page_watermarks_removed += removed
                                            new_content = pikepdf.unparse_content_stream(new_operations)
                                            new_stream = pikepdf.Stream(pdf, new_content)
                                            new_contents.append(new_stream)
                                        else:
                                            new_contents.append(stream)
                                            
                                    except Exception as e:
                                        new_contents.append(stream)
                                else:
                                    new_contents.append(stream)
                            
                            if page_watermarks_removed > 0:
                                page["/Contents"] = pikepdf.Array(new_contents)
                        
                        else:
                            # Single content stream
                            try:
                                operations = list(pikepdf.parse_content_stream(contents, ""))
                                new_operations, removed = process_operations_ultimate(operations, page_num)
                                
                                if removed > 0:
                                    page_watermarks_removed = removed
                                    new_content = pikepdf.unparse_content_stream(new_operations)
                                    page["/Contents"] = pikepdf.Stream(pdf, new_content)
                                    
                            except Exception as e:
                                pass
                        
                        if page_watermarks_removed > 0:
                            pages_processed += 1
                            total_watermarks_removed += page_watermarks_removed
                            
                            if page_num < 50 or page_watermarks_removed > 0:  # Print for pages with watermarks
                                print(f"  Page {page_num + 1}: Removed {page_watermarks_removed} watermark text blocks")
                    
                    # Progress indicator
                    if (page_num + 1) % 100 == 0:
                        print(f"  Processed {page_num + 1} pages... (removed {total_watermarks_removed} watermarks so far)")
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages with watermarks: {pages_processed}")
            print(f"  Total watermark text blocks removed: {total_watermarks_removed}")
            if pages_processed > 0:
                print(f"  Average watermarks per page: {total_watermarks_removed/pages_processed:.1f}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def process_operations_ultimate(operations, page_num):
    """
    Process operations and remove watermark text blocks with 30° rotation
    """
    new_operations = []
    watermarks_removed = 0
    
    i = 0
    while i < len(operations):
        operands, operator = operations[i]
        op_str = str(operator)
        
        if op_str == "BT":  # Begin text block
            # Collect entire text block
            text_block_ops = [(operands, operator)]
            i += 1
            
            is_watermark_block = False
            
            # Read until ET (End text)
            while i < len(operations):
                operands, operator = operations[i]
                op_str = str(operator)
                text_block_ops.append((operands, operator))
                
                # Check for watermark matrix (30° rotation)
                if op_str == "Tm" and is_watermark_matrix(operands):
                    is_watermark_block = True
                
                if op_str == "ET":  # End text block
                    break
                i += 1
            
            # Remove watermark blocks, keep others
            if is_watermark_block:
                watermarks_removed += 1
                if page_num < 10:  # Debug for first 10 pages
                    print(f"    Removing 30° rotated watermark text block")
                # Skip this text block
            else:
                # Keep this text block
                new_operations.extend(text_block_ops)
        
        else:
            # Non-text operations, keep them
            new_operations.append((operands, operator))
        
        i += 1
    
    return new_operations, watermarks_removed

def main():
    if len(sys.argv) != 3:
        print("Usage: python ultimate_watermark_remover.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not remove_watermarks_ultimate(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Ultimate watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
