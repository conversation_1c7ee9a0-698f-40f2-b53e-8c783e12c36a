#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Low-level watermark removal by directly manipulating content streams
"""
import sys
import pikepdf
import re

def low_level_remove_watermarks(input_pdf, output_pdf):
    """
    Remove watermarks by directly editing the raw content stream
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    print(f"\nPage {page_num + 1}:")
                    
                    # Get raw content stream
                    if hasattr(page, 'contents'):
                        if isinstance(page.contents, list):
                            # Multiple content streams
                            print("  Multiple content streams found")
                            for i, content in enumerate(page.contents):
                                if hasattr(content, 'read_bytes'):
                                    raw_content = content.read_bytes()
                                    print(f"    Content stream {i}: {len(raw_content)} bytes")
                        else:
                            # Single content stream
                            if hasattr(page.contents, 'read_bytes'):
                                raw_content = page.contents.read_bytes()
                                print(f"  Single content stream: {len(raw_content)} bytes")
                                
                                # Decode content
                                try:
                                    content_text = raw_content.decode('utf-8', errors='ignore')
                                    
                                    # Look for watermark patterns
                                    watermark_pattern = "KWWSV˛JLWKXEFRPFKHQ\\]˝˙)OXHQW3\\WKRQQG&1"
                                    
                                    if watermark_pattern in content_text:
                                        print(f"    *** FOUND WATERMARK PATTERN ***")
                                        
                                        # Remove the watermark pattern and surrounding TJ operations
                                        # Pattern: TJ [(watermark_pattern)] TJ
                                        pattern_to_remove = rf'\[\s*\(\s*{re.escape(watermark_pattern)}\s*\)\s*\]\s*TJ'
                                        
                                        new_content = re.sub(pattern_to_remove, '', content_text, flags=re.MULTILINE)
                                        
                                        if len(new_content) < len(content_text):
                                            print(f"    Removed {len(content_text) - len(new_content)} characters")
                                            
                                            # Create new content stream
                                            new_content_bytes = new_content.encode('utf-8')
                                            page.contents = pikepdf.Stream(pdf, new_content_bytes)
                                            pages_processed += 1
                                            print(f"    ✅ Content stream updated")
                                        else:
                                            print(f"    No changes made")
                                    else:
                                        print(f"    No watermark pattern found")
                                        
                                        # Also check for Do operations
                                        if "/Fm0 Do" in content_text or "/Fm1 Do" in content_text:
                                            print(f"    Found Do operations for watermark XObjects")
                                            
                                            # Remove Do operations
                                            new_content = re.sub(r'/Fm[0-9]+\s+Do', '', content_text)
                                            
                                            if len(new_content) < len(content_text):
                                                print(f"    Removed Do operations")
                                                new_content_bytes = new_content.encode('utf-8')
                                                page.contents = pikepdf.Stream(pdf, new_content_bytes)
                                                pages_processed += 1
                                                print(f"    ✅ Content stream updated")
                                    
                                except Exception as e:
                                    print(f"    Error processing content: {e}")
                            else:
                                print("  Content stream not readable")
                    else:
                        print("  No content stream found")
                    
                    # Only process first few pages for testing
                    if page_num >= 2:
                        break
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python low_level_remove.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not low_level_remove_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Low-level watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
