# SPDX-FileCopyrightText: 2025 <PERSON><PERSON>
# SPDX-License-Identifier: MPL-2.0

# This mapping comes from appendix D of the PDF 2.0 spec. However, I doubt it is
# exhaustive. It may be that an exhaustive list is not possible.
from __future__ import annotations

CHARNAMES_TO_UNICODE = {
    #### Table D.2 ####
    '/A': 'A',
    '/OE': 'Œ',
    '/AE': 'Æ',
    '/Oacute': 'Ó',
    '/Aacute': 'Á',
    '/Ocircumflex': 'Ô',
    '/Acircumflex': 'Â',
    '/Odieresis': 'Ö',
    '/Adieresis': 'Ä',
    '/Ograve': 'Ò',
    '/Agrave': 'À',
    '/Oslash': 'Ø',
    '/Aring': 'Å',
    '/Otilde': 'Õ',
    '/Atilde': 'Ã',
    '/P': 'P',
    '/B': 'B',
    '/Q': 'Q',
    '/C': 'C',
    '/R': 'R',
    '/Ccedilla': 'Ç',
    '/S': 'S',
    '/D': 'D',
    '/Scaron': 'Š',
    '/E': 'E',
    '/T': 'T',
    '/Eacute': 'É',
    '/Thorn': 'Þ',
    '/Ecircumflex': 'Ê',
    '/U': 'U',
    '/Edieresis': 'Ë',
    '/Uacute': 'Ú',
    '/Egrave': 'È',
    '/Ucircumflex': 'Û',
    '/Eth': 'Ð',
    '/Udieresis': 'Ü',
    '/Euro': '€',
    '/Ugrave': 'Ù',
    '/F': 'F',
    '/V': 'V',
    '/G': 'G',
    '/W': 'W',
    '/H': 'H',
    '/X': 'X',
    '/I': 'I',
    '/Y': 'Y',
    '/Iacute': 'Í',
    '/Yacute': 'Ý',
    '/Icircumflex': 'Î',
    '/Ydieresis': 'Ÿ',
    '/Idieresis': 'Ï',
    '/Z': 'Z',
    '/Igrave': 'Ì',
    '/Zcaron': 'Ž',
    '/J': 'J',
    '/a': 'a',
    '/K': 'K',
    '/aacute': 'á',
    '/L': 'L',
    '/acircumflex': 'â',
    '/Lslash': 'Ł',
    '/acute': '´',
    '/M': 'M',
    '/adieresis': 'ä',
    '/N': 'N',
    '/ae': 'æ',
    '/Ntilde': 'Ñ',
    '/agrave': 'à',
    '/O': 'O',
    '/ampersand': '&',
    '/aring': 'å',
    '/eth': 'ð',
    '/asciicircum': '^',
    '/exclam': '!',
    '/asciitilde': '~',
    '/exclamdown': '¡',
    '/asterisk': '*',
    '/f': 'f',
    '/at': '@',
    '/fi': 'ﬁ',
    '/atilde': 'ã',
    '/five': '5',
    '/b': 'b',
    '/fl': 'ﬂ',
    '/backslash': '\\',
    '/florin': 'ƒ',
    '/bar': '|',
    '/four': '4',
    '/braceleft': '{',
    '/fraction': '⁄',
    '/braceright': '}',
    '/g': 'g',
    '/bracketleft': '[',
    '/germandbls': 'ß',
    '/bracketright': ']',
    '/grave': '`',
    '/breve': '˘',
    '/greater': '>',
    '/brokenbar': '¦',
    '/guillemotleft': '«',
    '/bullet': '•',
    '/guillemotright': '»',
    '/c': 'c',
    '/guilsinglleft': '‹',
    '/caron': 'ˇ',
    '/guilsinglright': '›',
    '/ccedilla': 'ç',
    '/h': 'h',
    '/cedilla': '¸',
    '/hungarumlaut': '˝',
    '/cent': '¢',
    '/hyphen': '-',
    '/circumflex': 'ˆ',
    '/i': 'i',
    '/colon': ':',
    '/iacute': 'í',
    '/comma': ',',
    '/icircumflex': 'î',
    '/copyright': '©',
    '/idieresis': 'ï',
    '/currency': '¤',
    '/igrave': 'ì',
    '/d': 'd',
    '/j': 'j',
    '/dagger': '†',
    '/k': 'k',
    '/daggerdbl': '‡',
    '/l': 'l',
    '/degree': '°',
    '/less': '<',
    '/dieresis': '¨',
    '/logicalnot': '¬',
    '/divide': '÷',
    '/lslash': 'ł',
    '/dollar': '$',
    '/m': 'm',
    '/dotaccent': '˙',
    '/macron': '¯',
    '/dotlessi': 'ı',
    '/minus': '−',
    '/e': 'e',
    '/mu': 'μ',
    '/eacute': 'é',
    '/multiply': '×',
    '/ecircumflex': 'ê',
    '/n': 'n',
    '/edieresis': 'ë',
    '/nine': '9',
    '/egrave': 'è',
    '/ntilde': 'ñ',
    '/eight': '8',
    '/numbersign': '#',
    '/ellipsis': '…',
    '/o': 'o',
    '/emdash': '—',
    '/oacute': 'ó',
    '/endash': '–',
    '/ocircumflex': 'ô',
    '/equal': '=',
    '/odieresis': 'ö',
    '/oe': 'œ',
    '/s': 's',
    '/ogonek': '˛',
    '/scaron': 'š',
    '/ograve': 'ò',
    '/section': '§',
    '/one': '1',
    '/semicolon': ';',
    '/onehalf': '½',
    '/seven': '7',
    '/onequarter': '¼',
    '/six': '6',
    '/onesuperior': '¹',
    '/slash': '/',
    '/ordfeminine': 'ª',
    '/space': ' ',
    '/ordmasculine': 'º',
    '/sterling': '£',
    '/oslash': 'ø',
    '/t': 't',
    '/otilde': 'õ',
    '/thorn': 'þ',
    '/p': 'p',
    '/three': '3',
    '/paragraph': '¶',
    '/threequarters': '¾',
    '/parenleft': '(',
    '/threesuperior': '³',
    '/parenright': ')',
    '/tilde': '˜',
    '/percent': '%',
    '/trademark': '™',
    '/period': '.',
    '/two': '2',
    '/periodcentered': '·',
    '/twosuperior': '²',
    '/perthousand': '‰',
    '/u': 'u',
    '/plus': '+',
    '/uacute': 'ú',
    '/plusminus': '±',
    '/ucircumflex': 'û',
    '/q': 'q',
    '/udieresis': 'ü',
    '/question': '?',
    '/ugrave': 'ù',
    '/questiondown': '¿',
    '/underscore': '_',
    '/quotedbl': '"',
    '/v': 'v',
    '/quotedblbase': '„',
    '/w': 'w',
    '/quotedblleft': '“',
    '/x': 'x',
    '/quotedblright': '”',
    '/y': 'y',
    '/quoteleft': '‘',
    '/yacute': 'ý',
    '/quoteright': '’',
    '/ydieresis': 'ÿ',
    '/quotesinglbase': '‚',
    '/yen': '¥',
    '/quotesingle': "'",
    '/z': 'z',
    '/r': 'r',
    '/zcaron': 'ž',
    '/registered': '®',
    '/zero': '0',
    '/ring': '˚',
    #### Table D.4 ####
    # Much of this table is commented out on account of (as far as I can tell) not
    # mapping to real unicode values. The values below seem to be substitutions I got
    # when copy/pasting from the spec. Such values might still be useful if our intent
    # was to convert PDF to text, but currently our intent is only to do the inverse.
    # '/AEsmall': 'ᴁ',
    # '/Jsmall': 'ᴊ',
    # '/Aacutesmall': 'Á',
    # '/Ksmall': 'ᴋ',
    # '/Acircumflexsmall': 'Â',
    # '/Lslashsmall': 'ᴌ',
    # '/Acutesmall': '´',
    # '/Lsmall': 'ʟ',
    # '/Adieresissmall': 'Ä',
    # '/Macronsmall': '¯',
    # '/Agravesmall': 'À',
    # '/Msmall': 'ᴍ',
    # '/Aringsmall': 'Å',
    # '/Nsmall': 'ɴ',
    # '/Asmall': 'ᴀ',
    # '/Ntildesmall': 'Ñ',
    # '/Atildesmall': 'Ã',
    # '/OEsmall': 'ɶ',
    # '/Brevesmall': '˘',
    # '/Oacutesmall': 'Ó',
    # '/Bsmall': 'ʙ',
    # '/Ocircumflexsmall': 'Ô',
    # '/Caronsmall': 'ˇ',
    # '/Odieresissmall': 'Ö',
    # '/Ccedillasmall': 'Ç',
    # '/Ogoneksmall': '˛',
    # '/Cedillasmall': '¸',
    # '/Ogravesmall': 'ò',
    # '/Circumflexsmall': 'ˆ',
    # '/Oslashsmall': 'ø',
    # '/Csmall': 'ᴄ',
    # '/Osmall': 'ᴏ',
    # '/Dieresissmall': '¨',
    # '/Otildesmall': 'Õ',
    # '/Dotaccentsmall': '˙',
    # '/Psmall': 'ᴘ',
    # '/Dsmall': 'ᴅ',
    # '/Qsmall': 'Q',
    # '/Eacutesmall': 'É',
    # '/Ringsmall': '˚',
    # '/Ecircumflexsmall': 'Ê',
    # '/Rsmall': 'R',
    # '/Edieresissmall': 'Ë',
    # '/Scaronsmall': 'Š',
    # '/Egravesmall': 'È',
    # '/Ssmall': 'S',
    # '/Esmall': 'ᴇ',
    # '/Thornsmall': 'þ',
    # '/Ethsmall': 'ᴆ',
    # '/Tildesmall': '˜',
    # '/Fsmall': 'F',
    # '/Tsmall': 'ᴛ',
    # '/Gravesmall': '`',
    # '/Uacutesmall': 'Ú',
    # '/Gsmall': 'G',
    # '/Ucircumflexsmall': 'Û',
    # '/Hsmall': 'ʜ',
    # '/Udieresissmall': 'Ü',
    # '/Hungarumlautsmall': '˝',
    # '/Ugravesmall': 'Ù',
    # '/Iacutesmall': 'Í',
    # '/Usmall': 'ᴜ',
    # '/Icircumflexsmall': 'Î',
    # '/Vsmall': 'ᴠ',
    # '/Idieresissmall': 'Ï',
    # '/Wsmall': 'ᴡ',
    # '/Igravesmall': 'Ì',
    # '/Xsmall': 'X',
    # '/Ismall': 'I',
    # '/Yacutesmall': 'Ý',
    # '/Ydieresissmall': 'Ÿ',
    # '/fouroldstyle': '4',
    # '/Ysmall': 'Y',
    '/foursuperior': '⁴',
    # '/Zcaronsmall': 'Ž',
    # '/Zsmall': 'z',
    # '/ampersandsmall': '&',
    # '/hypheninferior': '-',
    # '/asuperior': 'a',
    # '/hyphensuperior': '-',
    # '/bsuperior': 'b',
    # '/isuperior': 'i',
    # '/centinferior': '¢',
    # '/lsuperior': 'l',
    # '/centoldstyle': '¢',
    # '/msuperior': 'm',
    # '/centsuperior': '¢',
    '/nineinferior': '₉',
    # '/nineoldstyle': '9',
    '/colonmonetary': '₡',
    '/ninesuperior': '⁹',
    '/nsuperior': 'ⁿ',
    # '/commainferior': ',',
    '/onedotenleader': '․',
    # '/commasuperior': ',',
    '/oneeighth': '⅛',
    # '/dollarinferior': '$',
    '/onefitted': '1',
    # '/dollaroldstyle': '$',
    # '/dollarsuperior': '$',
    '/oneinferior': '₁',
    # '/dsuperior': 'd',
    # '/oneoldstyle': '1',
    '/eightinferior': '₈',
    # '/eightoldstyle': '8',
    '/eightsuperior': '⁸',
    '/onethird': '⅓',
    # '/esuperior': 'e',
    # '/osuperior': 'O',
    # '/exclamdownsmall': '¡',
    '/parenleftinferior': '₍',
    # '/exclamsmall': '!',
    '/parenleftsuperior': '⁽',
    '/ff': 'ﬀ',
    '/parenrightinferior': '₎',
    '/ffi': 'ﬃ',
    '/parenrightsuperior': '⁾',
    '/ffl': 'ﬄ',
    # '/periodinferior': '.',
    '/figuredash': '‒',
    # '/periodsuperior': '.',
    '/fiveeighths': '⅝',
    # '/questiondownsmall': '¿',
    '/fiveinferior': '₅',
    # '/questionsmall': '?',
    # '/fiveoldstyle': '5',
    # '/rsuperior': 'r',
    '/fivesuperior': '⁵',
    # '/rupiah': 'Rp',
    '/fourinferior': '₄',
    '/seveneighths': '⅞',
    '/seveninferior': '₇',
    # '/threequartersemdash': '—',
    # '/sevenoldstyle': '7',
    '/sevensuperior': '⁷',
    # '/tsuperior': 't',
    '/sixinferior': '₆',
    '/twodotenleader': '‥',
    # '/sixoldstyle': '6',
    '/twoinferior': '₂',
    '/sixsuperior': '⁶',
    # '/twooldstyle': '2',
    # '/ssuperior': 'S',
    '/twothirds': '⅔',
    '/threeeighths': '⅜',
    '/zeroinferior': '₀',
    '/threeinferior': '₃',
    # '/zerooldstyle': '0',
    # '/threeoldstyle': '3',
    '/zerosuperior': '⁰',
    #### Table D.5 ####
    '/Alpha': 'Α',
    '/arrowboth': '↔',
    '/Beta': 'Β',
    '/arrowdblboth': '⇔',
    '/Chi': 'Χ',
    '/arrowdbldown': '⇓',
    '/Delta': 'Δ',
    '/arrowdblleft': '⇐',
    '/Epsilon': 'Ε',
    '/arrowdblright': '⇒',
    '/Eta': 'Η',
    '/arrowdblup': '⇑',
    '/arrowdown': '↓',
    '/Gamma': 'Γ',
    '/arrowhorizex': '⎯',
    '/Ifraktur': 'ℑ',
    '/arrowleft': '←',
    '/Iota': 'Ι',
    '/arrowright': '→',
    '/Kappa': 'Κ',
    '/arrowup': '↑',
    '/Lambda': 'Λ',
    '/arrowvertex': '⏐',
    '/Mu': 'Μ',
    '/asteriskmath': '∗',
    '/Nu': 'Ν',
    '/Omega': 'Ω',
    '/beta': 'β',
    '/Omicron': 'Ο',
    '/Phi': 'Φ',
    '/Pi': 'Π',
    '/bracelefttp': '⎧',
    '/Psi': 'Ψ',
    '/braceleftmid': '⎨',
    '/Rfraktur': 'ℜ',
    '/braceleftbt': '⎩',
    '/Rho': 'Ρ',
    '/bracerighttp': '⎫',
    '/Sigma': 'Σ',
    '/bracerightmid': '⎬',
    '/Tau': 'Τ',
    '/bracerightbt': '⎭',
    '/Theta': 'Θ',
    '/braceex': '⎪',
    '/Upsilon': 'Υ',
    '/Upsilon1': 'ϒ',
    '/Xi': 'Ξ',
    '/bracketlefttp': '⎡',
    '/Zeta': 'Ζ',
    '/bracketleftex': '⎢',
    '/aleph': 'ℵ',
    '/bracketleftbt': '⎣',
    '/alpha': 'α',
    '/bracketrighttp': '⎤',
    '/bracketrightex': '⎥',
    '/angle': '∠',
    '/bracketrightbt': '⎦',
    '/angleleft': '〈',
    '/angleright': '〉',
    '/carriagereturn': '↵',
    '/approxequal': '≈',
    '/chi': 'χ',
    '/circlemultiply': '⊗',
    '/integralbt': '⌡',
    '/circleplus': '⊕',
    '/intersection': '∩',
    '/club': '♣',
    '/iota': 'ι',
    '/kappa': 'κ',
    '/lambda': 'λ',
    '/congruent': '≅',
    '/copyrightsans': '©',
    '/lessequal': '≤',
    '/copyrightserif': '©',
    '/logicaland': '∧',
    '/delta': 'δ',
    '/logicalor': '∨',
    '/diamond': '♦',
    '/lozenge': '◊',
    '/dotmath': '⋅',
    '/minute': '′',
    '/element': '∈',
    '/emptyset': '∅',
    '/notelement': '∉',
    '/epsilon': 'ε',
    '/notequal': '≠',
    '/notsubset': '⊄',
    '/equivalence': '≡',
    '/nu': 'ν',
    '/eta': 'η',
    '/omega': 'ω',
    '/existential': '∃',
    '/omega1': 'ϖ',
    '/omicron': 'ο',
    '/gamma': 'γ',
    '/parenlefttp': '⎛',
    '/gradient': '∇',
    '/parenleftex': '⎜',
    '/parenleftbt': '⎝',
    '/greaterequal': '≥',
    '/parenrighttp': '⎞',
    '/heart': '♥',
    '/parenrightex': '⎟',
    '/infinity': '∞',
    '/parenrightbt': '⎠',
    '/integral': '∫',
    '/partialdiff': '∂',
    '/integraltp': '⌠',
    '/integralex': '⎮',
    '/perpendicular': '⊥',
    '/similar': '∼',
    '/phi': 'ϕ',
    '/phi1': 'φ',
    '/pi': 'π',
    '/spade': '♠',
    '/suchthat': '∋',
    '/product': 'Π',
    '/summation': 'Σ',
    '/propersubset': '⊂',
    '/tau': 'τ',
    '/propersuperset': '⊃',
    '/therefore': '∴',
    '/proportional': '∝',
    '/theta': 'θ',
    '/psi': 'ψ',
    '/theta1': 'ϑ',
    '/radical': '√',
    '/trademarksans': '™',
    '/radicalex': '⎯',
    '/trademarkserif': '™',
    '/reflexsubset': '⊆',
    '/reflexsuperset': '⊇',
    '/registersans': '®',
    '/union': '∪',
    '/registerserif': '®',
    '/universal': '∀',
    '/rho': 'ρ',
    '/upsilon': 'υ',
    '/second': '″',
    '/weierstrass': '℘',
    '/xi': 'ξ',
    '/sigma': 'σ',
    '/zeta': 'ζ',
    '/sigma1': 'ς',
    #### Table D.6 ####
    '/a30': '✣',
    '/a65': '❆',
    '/a109': '♠',
    '/a1': '✁',
    '/a31': '✤',
    '/a66': '❇',
    '/a120': '①',
    '/a2': '✂',
    '/a32': '✥',
    '/a67': '❈',
    '/a121': '②',
    '/a202': '✃',
    '/a33': '✦',
    '/a68': '❉',
    '/a122': '③',
    '/a3': '✄',
    '/a34': '✧',
    '/a69': '❊',
    '/a123': '④',
    '/a4': '☎',
    '/a35': '★',
    '/a70': '❋',
    '/a124': '⑤',
    '/a5': '✆',
    '/a36': '✩',
    '/a71': '●',
    '/a125': '⑥',
    '/a119': '✇',
    '/a37': '✪',
    '/a72': '❍',
    '/a126': '⑦',
    '/a118': '✈',
    '/a38': '✫',
    '/a73': '■',
    '/a127': '⑧',
    '/a117': '✉',
    '/a39': '✬',
    '/a74': '❏',
    '/a128': '⑨',
    '/a11': '☛',
    '/a40': '✭',
    '/a203': '❐',
    '/a129': '⑩',
    '/a12': '☞',
    '/a41': '✮',
    '/a75': '❑',
    '/a130': '❶',
    '/a13': '✌',
    '/a42': '✯',
    '/a204': '❒',
    '/a131': '❷',
    '/a14': '✍',
    '/a43': '✰',
    '/a76': '▲',
    '/a132': '❸',
    '/a15': '✎',
    '/a44': '✱',
    '/a77': '▼',
    '/a133': '❹',
    '/a16': '✏',
    '/a45': '✲',
    '/a78': '◆',
    '/a134': '❺',
    '/a105': '✐',
    '/a46': '✳',
    '/a79': '❖',
    '/a135': '❻',
    '/a17': '✑',
    '/a47': '✴',
    '/a81': '◗',
    '/a136': '❼',
    '/a18': '✒',
    '/a48': '✵',
    '/a82': '❘',
    '/a137': '❽',
    '/a19': '✓',
    '/a49': '✶',
    '/a83': '❙',
    '/a138': '❾',
    '/a20': '✔',
    '/a50': '✷',
    '/a84': '❚',
    '/a139': '❿',
    '/a21': '✕',
    '/a51': '✸',
    '/a97': '❛',
    '/a140': '➀',
    '/a22': '✖',
    '/a52': '✹',
    '/a98': '❜',
    '/a141': '➁',
    '/a23': '✗',
    '/a53': '✺',
    '/a99': '❝',
    '/a142': '➂',
    '/a24': '✘',
    '/a54': '✻',
    '/a100': '❞',
    '/a143': '➃',
    '/a25': '✙',
    '/a55': '✼',
    '/a101': '❡',
    '/a144': '➄',
    '/a26': '✚',
    '/a56': '✽',
    '/a102': '❢',
    '/a145': '➅',
    '/a27': '✛',
    '/a57': '✾',
    '/a103': '❣',
    '/a146': '➆',
    '/a28': '✜',
    '/a58': '✿',
    '/a104': '❤',
    '/a147': '➇',
    '/a6': '✝',
    '/a59': '❀',
    '/a106': '❥',
    '/a148': '➈',
    '/a7': '✞',
    '/a60': '❁',
    '/a107': '❦',
    '/a149': '➉',
    '/a8': '✟',
    '/a61': '❂',
    '/a108': '❧',
    '/a150': '➊',
    '/a9': '✠',
    '/a62': '❃',
    '/a112': '♣',
    '/a151': '➋',
    '/a10': '✡',
    '/a63': '❄',
    '/a111': '♦',
    '/a152': '➌',
    '/a29': '✢',
    '/a64': '❅',
    '/a110': '♥',
    '/a153': '➍',
    '/a154': '➎',
    '/a192': '➚',
    '/a176': '➦',
    '/a184': '➳',
    '/a155': '➏',
    '/a166': '➛',
    '/a177': '➧',
    '/a197': '➴',
    '/a156': '➐',
    '/a167': '➜',
    '/a178': '➨',
    '/a185': '➵',
    '/a157': '➑',
    '/a168': '➝',
    '/a179': '➩',
    '/a194': '➶',
    '/a158': '➒',
    '/a169': '➞',
    '/a193': '➪',
    '/a198': '➷',
    '/a159': '➓',
    '/a170': '➟',
    '/a180': '➫',
    '/a186': '➸',
    '/a160': '➔',
    '/a171': '➠',
    '/a199': '➬',
    '/a195': '➹',
    '/a161': '→',
    '/a172': '➡',
    '/a181': '➭',
    '/a187': '➺',
    '/a163': '↔',
    '/a173': '➢',
    '/a200': '➮',
    '/a188': '➻',
    '/a164': '↕',
    '/a162': '➣',
    '/a182': '➯',
    '/a189': '➼',
    '/a196': '➘',
    '/a174': '➤',
    '/a201': '➱',
    '/a190': '➽',
    '/a165': '➙',
    '/a175': '➥',
    '/a183': '➲',
    '/a191': '➾',
}
