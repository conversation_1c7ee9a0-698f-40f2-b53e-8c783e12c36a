#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Direct watermark removal with proper content stream handling
"""
import sys
import pikepdf

def direct_remove_watermarks(input_pdf, output_pdf):
    """
    Directly remove watermarks with proper content stream handling
    """
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    # Get original operations
                    original_operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    print(f"\nPage {page_num + 1}:")
                    print(f"  Original operations: {len(original_operations)}")
                    
                    for i, (operands, operator) in enumerate(original_operations):
                        should_remove = False
                        
                        # Remove TJ operations with watermark patterns
                        if str(operator) == "TJ":
                            try:
                                operands_str = str(operands)
                                watermark_patterns = ["KWWSV", "JLWKX", "FKHQ", "OXHQW", "WKRQ"]
                                
                                if any(pattern in operands_str for pattern in watermark_patterns):
                                    should_remove = True
                                    operations_removed += 1
                                    page_modified = True
                                    print(f"    Removing TJ operation {i} (watermark)")
                            except:
                                pass
                        
                        # Remove Do operations for watermark XObjects
                        elif str(operator) == "Do" and operands:
                            xobj_name = str(operands[0])
                            watermark_xobjects = ["/Fm0", "/Fm1", "/X4", "/Image1"]
                            
                            if xobj_name in watermark_xobjects:
                                should_remove = True
                                operations_removed += 1
                                page_modified = True
                                print(f"    Removing Do operation {i} ({xobj_name})")
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    print(f"  New operations: {len(new_operations)}")
                    print(f"  Operations removed: {len(original_operations) - len(new_operations)}")
                    
                    # Update page content if modified
                    if page_modified:
                        try:
                            # Method 1: Try direct assignment
                            new_content_stream = pikepdf.unparse_content_stream(new_operations)
                            page.contents = new_content_stream
                            pages_processed += 1
                            print(f"    ✅ Page content updated successfully")
                            
                            # Verify the change
                            verify_operations = list(pikepdf.parse_content_stream(page, ""))
                            print(f"    Verification: {len(verify_operations)} operations after update")
                            
                        except Exception as e:
                            print(f"    ❌ Failed to update page content: {e}")
                            
                            # Method 2: Try alternative approach
                            try:
                                # Clear existing content and set new content
                                if hasattr(page, 'contents'):
                                    if isinstance(page.contents, list):
                                        page.contents.clear()
                                    page.contents = pikepdf.Stream(pdf, new_content_stream)
                                    print(f"    ✅ Page content updated with alternative method")
                            except Exception as e2:
                                print(f"    ❌ Alternative method also failed: {e2}")
                    
                    # Only process first few pages for testing
                    if page_num >= 2:
                        break
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Operations removed: {operations_removed}")
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) != 3:
        print("Usage: python direct_remove_watermarks.py <input.pdf> <output.pdf>")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    if not direct_remove_watermarks(input_pdf, output_pdf):
        print("Failed to remove watermarks")
        sys.exit(1)
    
    print(f"\n🎉 Direct watermark removal completed!")
    print(f"Clean PDF saved as: {output_pdf}")

if __name__ == "__main__":
    main()
