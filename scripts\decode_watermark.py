#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decode and remove encoded watermark text
"""
import sys
import pikepdf
import string

def caesar_decode(text, shift):
    """Caesar cipher decoder"""
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            result += chr((ord(char) - ascii_offset - shift) % 26 + ascii_offset)
        else:
            result += char
    return result

def analyze_encoded_text(text_array):
    """Analyze and decode text array from TJ operations"""
    if not text_array:
        return None, None

    # Convert pikepdf Array to string representation
    array_str = str(text_array)

    # Check for suspicious patterns first
    suspicious_patterns = ["KWWSV", "JLWKX", "FKHQ", "IOXHQW", "3\\WKRQ"]
    has_suspicious = any(pattern in array_str for pattern in suspicious_patterns)

    if has_suspicious:
        # Extract text parts from the array string
        # Look for quoted strings in the array representation
        import re
        quoted_texts = re.findall(r'"([^"]*)"', array_str)

        full_text = ''.join(quoted_texts)

        # Try Caesar cipher decoding
        for shift in range(1, 26):
            decoded = caesar_decode(full_text, shift)
            if any(word in decoded.upper() for word in ["HTTPS", "GITHUB", "CHEN", "FLUENT", "PYTHON"]):
                return decoded, shift

        # If no clear decode, but has suspicious patterns, mark for removal
        return f"SUSPICIOUS_ENCODED: {full_text[:50]}", 0

    return None, None

def find_and_remove_encoded_watermarks(input_pdf, output_pdf):
    """Find and remove encoded watermark text"""
    print(f"Processing: {input_pdf}")
    print(f"Output: {output_pdf}")
    
    pages_processed = 0
    operations_removed = 0
    watermark_texts_found = set()
    
    try:
        with pikepdf.Pdf.open(input_pdf) as pdf:
            print(f"Total pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    operations = list(pikepdf.parse_content_stream(page, ""))
                    new_operations = []
                    page_modified = False
                    
                    for operands, operator in operations:
                        should_remove = False
                        
                        # Check TJ operations (text arrays)
                        if str(operator) == "TJ" and operands:
                            try:
                                text_array = operands[0]
                                decoded_text, shift = analyze_encoded_text(text_array)
                                
                                if decoded_text:
                                    watermark_texts_found.add(decoded_text)
                                    should_remove = True
                                    operations_removed += 1
                                    page_modified = True
                                    
                                    if page_num < 5:  # Print for first few pages
                                        print(f"  Page {page_num + 1}: Found encoded watermark (shift {shift}): {decoded_text}")
                                
                                # Also check for suspicious patterns
                                text_str = str(text_array)
                                if any(pattern in text_str for pattern in ["KWWSV", "JLWKX", "FKHQ"]):
                                    should_remove = True
                                    operations_removed += 1
                                    page_modified = True
                                    
                                    if page_num < 5:
                                        print(f"  Page {page_num + 1}: Found suspicious encoded text: {text_str[:50]}...")
                                        
                            except Exception as e:
                                pass
                        
                        # Check Tj operations (simple text)
                        elif str(operator) == "Tj" and operands:
                            try:
                                text = str(operands[0])
                                # Try to decode
                                for shift in range(1, 26):
                                    decoded = caesar_decode(text, shift)
                                    if "github.com" in decoded.lower() or "https" in decoded.lower():
                                        watermark_texts_found.add(decoded)
                                        should_remove = True
                                        operations_removed += 1
                                        page_modified = True
                                        
                                        if page_num < 5:
                                            print(f"  Page {page_num + 1}: Found encoded Tj watermark: {decoded}")
                                        break
                            except:
                                pass
                        
                        if not should_remove:
                            new_operations.append((operands, operator))
                    
                    # Update page content if modified
                    if page_modified:
                        page.contents = pikepdf.unparse_content_stream(new_operations)
                        pages_processed += 1
                        
                except Exception as e:
                    print(f"Error processing page {page_num + 1}: {e}")
                    continue
            
            # Save the result
            pdf.save(output_pdf)
            
            print(f"\nSummary:")
            print(f"  Pages processed: {pages_processed}")
            print(f"  Text operations removed: {operations_removed}")
            print(f"  Unique watermark texts found: {len(watermark_texts_found)}")
            
            if watermark_texts_found:
                print(f"  Decoded watermarks:")
                for text in sorted(watermark_texts_found):
                    print(f"    - {text}")
            
            print(f"  Output saved: {output_pdf}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

def test_decoding():
    """Test the decoding logic"""
    print("Testing decoding logic:")
    
    # Test known patterns
    test_cases = [
        "KWWSV",  # Should decode to HTTPS with some shift
        "JLWKX",  # Should decode to GITHUB with some shift
        "FKHQ",   # Should decode to CHEN with some shift
    ]
    
    for test in test_cases:
        print(f"\nTesting: {test}")
        for shift in range(1, 26):
            decoded = caesar_decode(test, shift)
            if any(word in decoded.upper() for word in ["HTTPS", "GITHUB", "CHEN"]):
                print(f"  Shift {shift}: {test} -> {decoded}")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        test_decoding()
    elif len(sys.argv) == 3:
        input_pdf = sys.argv[1]
        output_pdf = sys.argv[2]
        
        if not find_and_remove_encoded_watermarks(input_pdf, output_pdf):
            print("Failed to remove watermarks")
            sys.exit(1)
        
        print(f"\n🎉 Encoded watermark removal completed!")
        print(f"Clean PDF saved as: {output_pdf}")
    else:
        print("Usage: python decode_watermark.py <input.pdf> <output.pdf>")
        print("   or: python decode_watermark.py  (to test decoding)")
        sys.exit(1)
